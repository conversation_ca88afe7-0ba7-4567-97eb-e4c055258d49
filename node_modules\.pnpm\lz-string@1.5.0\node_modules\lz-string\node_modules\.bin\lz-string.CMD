@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\lz-string@1.5.0\node_modules\lz-string\bin\node_modules;E:\code\work\components\node_modules\.pnpm\lz-string@1.5.0\node_modules\lz-string\node_modules;E:\code\work\components\node_modules\.pnpm\lz-string@1.5.0\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\lz-string@1.5.0\node_modules\lz-string\bin\node_modules;E:\code\work\components\node_modules\.pnpm\lz-string@1.5.0\node_modules\lz-string\node_modules;E:\code\work\components\node_modules\.pnpm\lz-string@1.5.0\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\bin.js" %*
)
