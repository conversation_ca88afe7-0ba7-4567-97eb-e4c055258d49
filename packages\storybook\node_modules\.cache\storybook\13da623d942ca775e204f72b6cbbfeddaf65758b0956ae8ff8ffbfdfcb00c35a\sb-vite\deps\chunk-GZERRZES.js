import {
  __export
} from "./chunk-APLEHN7L.js";
import {
  createApp,
  h,
  isReactive,
  isVNode,
  reactive
} from "./chunk-Y4H6EBV6.js";
import {
  require_preview_api
} from "./chunk-RF4OLZDA.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/@storybook+vue3@8.6.14_storybook@8.6.14_vue@3.5.17/node_modules/@storybook/vue3/dist/chunk-IBPFZ7LW.mjs
var import_preview_api = __toESM(require_preview_api(), 1);
var entry_preview_exports = {};
__export(entry_preview_exports, { applyDecorators: () => decorateStory, mount: () => mount, parameters: () => parameters, render: () => render, renderToCanvas: () => renderToCanvas });
var render = (props, context) => {
  let { id, component: Component } = context;
  if (!Component) throw new Error(`Unable to render story ${id} as the component annotation is missing from the default export`);
  return () => h(Component, props, getSlots(props, context));
};
var setup = (fn) => {
  globalThis.PLUGINS_SETUP_FUNCTIONS ??= /* @__PURE__ */ new Set(), globalThis.PLUGINS_SETUP_FUNCTIONS.add(fn);
};
var runSetupFunctions = async (app, storyContext) => {
  globalThis && globalThis.PLUGINS_SETUP_FUNCTIONS && await Promise.all([...globalThis.PLUGINS_SETUP_FUNCTIONS].map((fn) => fn(app, storyContext)));
};
var map = /* @__PURE__ */ new Map();
async function renderToCanvas({ storyFn, forceRemount, showMain, showException, storyContext, id }, canvasElement) {
  let existingApp = map.get(canvasElement);
  if (existingApp && !forceRemount) {
    let element = storyFn(), args = getArgs(element, storyContext);
    return updateArgs(existingApp.reactiveArgs, args), () => {
      teardown(existingApp.vueApp, canvasElement);
    };
  }
  existingApp && forceRemount && teardown(existingApp.vueApp, canvasElement);
  let vueApp = createApp({ setup() {
    storyContext.args = reactive(storyContext.args);
    let rootElement = storyFn(), args = getArgs(rootElement, storyContext), appState = { vueApp, reactiveArgs: reactive(args) };
    return map.set(canvasElement, appState), () => h(rootElement);
  } });
  return vueApp.config.errorHandler = (e, instance, info) => {
    window.__STORYBOOK_PREVIEW__?.storyRenders.some((renderer) => renderer.id === id && renderer.phase === "playing") ? setTimeout(() => {
      throw e;
    }, 0) : showException(e);
  }, await runSetupFunctions(vueApp, storyContext), vueApp.mount(canvasElement), showMain(), () => {
    teardown(vueApp, canvasElement);
  };
}
function getSlots(props, context) {
  let { argTypes } = context, slots = Object.entries(props).filter(([key]) => argTypes[key]?.table?.category === "slots").map(([key, value]) => [key, typeof value == "function" ? value : () => value]);
  return Object.fromEntries(slots);
}
function getArgs(element, storyContext) {
  return element.props && isVNode(element) ? element.props : storyContext.args;
}
function updateArgs(reactiveArgs, nextArgs) {
  if (Object.keys(nextArgs).length === 0) return;
  let currentArgs = isReactive(reactiveArgs) ? reactiveArgs : reactive(reactiveArgs);
  Object.keys(currentArgs).forEach((key) => {
    key in nextArgs || delete currentArgs[key];
  }), Object.assign(currentArgs, nextArgs);
}
function teardown(storybookApp, canvasElement) {
  storybookApp?.unmount(), map.has(canvasElement) && map.delete(canvasElement);
}
function normalizeFunctionalComponent(options) {
  return typeof options == "function" ? { render: options, name: options.name } : options;
}
function prepare(rawStory, innerStory) {
  let story = rawStory;
  return story === null ? null : typeof story == "function" ? story : innerStory ? { ...normalizeFunctionalComponent(story), components: { ...story.components || {}, story: innerStory } } : { render() {
    return h(story);
  } };
}
function decorateStory(storyFn, decorators) {
  return decorators.reduce((decorated, decorator) => (context) => {
    let story, decoratedStory = decorator((update) => {
      let sanitizedUpdate = (0, import_preview_api.sanitizeStoryContextUpdate)(update);
      return update && (sanitizedUpdate.args = Object.assign(context.args, sanitizedUpdate.args)), story = decorated({ ...context, ...sanitizedUpdate }), story;
    }, context);
    return story || (story = decorated(context)), decoratedStory === story ? story : prepare(decoratedStory, () => h(story));
  }, (context) => prepare(storyFn(context)));
}
var mount = (context) => async (Component, options) => (Component && (context.originalStoryFn = () => () => h(Component, options?.props, options?.slots)), await context.renderToCanvas(), context.canvas);
var parameters = { renderer: "vue3" };

export {
  entry_preview_exports,
  render,
  setup,
  renderToCanvas,
  decorateStory,
  mount,
  parameters
};
//# sourceMappingURL=chunk-GZERRZES.js.map
