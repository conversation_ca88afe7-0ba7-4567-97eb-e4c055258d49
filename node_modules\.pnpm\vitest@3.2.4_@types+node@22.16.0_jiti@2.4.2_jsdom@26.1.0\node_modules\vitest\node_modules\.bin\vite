#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/code/work/components/node_modules/.pnpm/vite@7.0.0_@types+node@22.16.0_jiti@2.4.2/node_modules/vite/bin/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/vite@7.0.0_@types+node@22.16.0_jiti@2.4.2/node_modules/vite/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/vite@7.0.0_@types+node@22.16.0_jiti@2.4.2/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/code/work/components/node_modules/.pnpm/vite@7.0.0_@types+node@22.16.0_jiti@2.4.2/node_modules/vite/bin/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/vite@7.0.0_@types+node@22.16.0_jiti@2.4.2/node_modules/vite/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/vite@7.0.0_@types+node@22.16.0_jiti@2.4.2/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../vite@7.0.0_@types+node@22.16.0_jiti@2.4.2/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../../vite@7.0.0_@types+node@22.16.0_jiti@2.4.2/node_modules/vite/bin/vite.js" "$@"
fi
