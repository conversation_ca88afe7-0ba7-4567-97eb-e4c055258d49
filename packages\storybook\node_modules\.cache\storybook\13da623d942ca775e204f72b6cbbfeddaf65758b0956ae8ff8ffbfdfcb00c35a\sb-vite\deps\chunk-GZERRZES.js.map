{"version": 3, "sources": ["../../../../../../../../node_modules/.pnpm/@storybook+vue3@8.6.14_storybook@8.6.14_vue@3.5.17/node_modules/@storybook/vue3/dist/chunk-IBPFZ7LW.mjs"], "sourcesContent": ["import { __export } from './chunk-CEH6MNVV.mjs';\nimport { h, createApp, reactive, isVNode, isReactive } from 'vue';\nimport { sanitizeStoryContextUpdate } from 'storybook/internal/preview-api';\n\nvar entry_preview_exports={};__export(entry_preview_exports,{applyDecorators:()=>decorateStory,mount:()=>mount,parameters:()=>parameters,render:()=>render,renderToCanvas:()=>renderToCanvas});var render=(props,context)=>{let{id,component:Component}=context;if(!Component)throw new Error(`Unable to render story ${id} as the component annotation is missing from the default export`);return ()=>h(Component,props,getSlots(props,context))},setup=fn=>{globalThis.PLUGINS_SETUP_FUNCTIONS??=new Set,globalThis.PLUGINS_SETUP_FUNCTIONS.add(fn);},runSetupFunctions=async(app,storyContext)=>{globalThis&&globalThis.PLUGINS_SETUP_FUNCTIONS&&await Promise.all([...globalThis.PLUGINS_SETUP_FUNCTIONS].map(fn=>fn(app,storyContext)));},map=new Map;async function renderToCanvas({storyFn,forceRemount,showMain,showException,storyContext,id},canvasElement){let existingApp=map.get(canvasElement);if(existingApp&&!forceRemount){let element=storyFn(),args=getArgs(element,storyContext);return updateArgs(existingApp.reactiveArgs,args),()=>{teardown(existingApp.vueApp,canvasElement);}}existingApp&&forceRemount&&teardown(existingApp.vueApp,canvasElement);let vueApp=createApp({setup(){storyContext.args=reactive(storyContext.args);let rootElement=storyFn(),args=getArgs(rootElement,storyContext),appState={vueApp,reactiveArgs:reactive(args)};return map.set(canvasElement,appState),()=>h(rootElement)}});return vueApp.config.errorHandler=(e,instance,info)=>{window.__STORYBOOK_PREVIEW__?.storyRenders.some(renderer=>renderer.id===id&&renderer.phase===\"playing\")?setTimeout(()=>{throw e},0):showException(e);},await runSetupFunctions(vueApp,storyContext),vueApp.mount(canvasElement),showMain(),()=>{teardown(vueApp,canvasElement);}}function getSlots(props,context){let{argTypes}=context,slots=Object.entries(props).filter(([key])=>argTypes[key]?.table?.category===\"slots\").map(([key,value])=>[key,typeof value==\"function\"?value:()=>value]);return Object.fromEntries(slots)}function getArgs(element,storyContext){return element.props&&isVNode(element)?element.props:storyContext.args}function updateArgs(reactiveArgs,nextArgs){if(Object.keys(nextArgs).length===0)return;let currentArgs=isReactive(reactiveArgs)?reactiveArgs:reactive(reactiveArgs);Object.keys(currentArgs).forEach(key=>{key in nextArgs||delete currentArgs[key];}),Object.assign(currentArgs,nextArgs);}function teardown(storybookApp,canvasElement){storybookApp?.unmount(),map.has(canvasElement)&&map.delete(canvasElement);}function normalizeFunctionalComponent(options){return typeof options==\"function\"?{render:options,name:options.name}:options}function prepare(rawStory,innerStory){let story=rawStory;return story===null?null:typeof story==\"function\"?story:innerStory?{...normalizeFunctionalComponent(story),components:{...story.components||{},story:innerStory}}:{render(){return h(story)}}}function decorateStory(storyFn,decorators){return decorators.reduce((decorated,decorator)=>context=>{let story,decoratedStory=decorator(update=>{let sanitizedUpdate=sanitizeStoryContextUpdate(update);return update&&(sanitizedUpdate.args=Object.assign(context.args,sanitizedUpdate.args)),story=decorated({...context,...sanitizedUpdate}),story},context);return story||(story=decorated(context)),decoratedStory===story?story:prepare(decoratedStory,()=>h(story))},context=>prepare(storyFn(context)))}var mount=context=>async(Component,options)=>(Component&&(context.originalStoryFn=()=>()=>h(Component,options?.props,options?.slots)),await context.renderToCanvas(),context.canvas);var parameters={renderer:\"vue3\"};\n\nexport { decorateStory, entry_preview_exports, mount, parameters, render, renderToCanvas, setup };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAEA,yBAA2C;AAE3C,IAAI,wBAAsB,CAAC;AAAE,SAAS,uBAAsB,EAAC,iBAAgB,MAAI,eAAc,OAAM,MAAI,OAAM,YAAW,MAAI,YAAW,QAAO,MAAI,QAAO,gBAAe,MAAI,eAAc,CAAC;AAAE,IAAI,SAAO,CAAC,OAAM,YAAU;AAAC,MAAG,EAAC,IAAG,WAAU,UAAS,IAAE;AAAQ,MAAG,CAAC,UAAU,OAAM,IAAI,MAAM,0BAA0B,EAAE,iEAAiE;AAAE,SAAO,MAAI,EAAE,WAAU,OAAM,SAAS,OAAM,OAAO,CAAC;AAAC;AAAnP,IAAqP,QAAM,QAAI;AAAC,aAAW,4BAA0B,oBAAI,OAAI,WAAW,wBAAwB,IAAI,EAAE;AAAE;AAAxV,IAA0V,oBAAkB,OAAM,KAAI,iBAAe;AAAC,gBAAY,WAAW,2BAAyB,MAAM,QAAQ,IAAI,CAAC,GAAG,WAAW,uBAAuB,EAAE,IAAI,QAAI,GAAG,KAAI,YAAY,CAAC,CAAC;AAAE;AAA/gB,IAAihB,MAAI,oBAAI;AAAI,eAAe,eAAe,EAAC,SAAQ,cAAa,UAAS,eAAc,cAAa,GAAE,GAAE,eAAc;AAAC,MAAI,cAAY,IAAI,IAAI,aAAa;AAAE,MAAG,eAAa,CAAC,cAAa;AAAC,QAAI,UAAQ,QAAQ,GAAE,OAAK,QAAQ,SAAQ,YAAY;AAAE,WAAO,WAAW,YAAY,cAAa,IAAI,GAAE,MAAI;AAAC,eAAS,YAAY,QAAO,aAAa;AAAA,IAAE;AAAA,EAAC;AAAC,iBAAa,gBAAc,SAAS,YAAY,QAAO,aAAa;AAAE,MAAI,SAAO,UAAU,EAAC,QAAO;AAAC,iBAAa,OAAK,SAAS,aAAa,IAAI;AAAE,QAAI,cAAY,QAAQ,GAAE,OAAK,QAAQ,aAAY,YAAY,GAAE,WAAS,EAAC,QAAO,cAAa,SAAS,IAAI,EAAC;AAAE,WAAO,IAAI,IAAI,eAAc,QAAQ,GAAE,MAAI,EAAE,WAAW;AAAA,EAAC,EAAC,CAAC;AAAE,SAAO,OAAO,OAAO,eAAa,CAAC,GAAE,UAAS,SAAO;AAAC,WAAO,uBAAuB,aAAa,KAAK,cAAU,SAAS,OAAK,MAAI,SAAS,UAAQ,SAAS,IAAE,WAAW,MAAI;AAAC,YAAM;AAAA,IAAC,GAAE,CAAC,IAAE,cAAc,CAAC;AAAA,EAAE,GAAE,MAAM,kBAAkB,QAAO,YAAY,GAAE,OAAO,MAAM,aAAa,GAAE,SAAS,GAAE,MAAI;AAAC,aAAS,QAAO,aAAa;AAAA,EAAE;AAAC;AAAC,SAAS,SAAS,OAAM,SAAQ;AAAC,MAAG,EAAC,SAAQ,IAAE,SAAQ,QAAM,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC,CAAC,GAAG,MAAI,SAAS,GAAG,GAAG,OAAO,aAAW,OAAO,EAAE,IAAI,CAAC,CAAC,KAAI,KAAK,MAAI,CAAC,KAAI,OAAO,SAAO,aAAW,QAAM,MAAI,KAAK,CAAC;AAAE,SAAO,OAAO,YAAY,KAAK;AAAC;AAAC,SAAS,QAAQ,SAAQ,cAAa;AAAC,SAAO,QAAQ,SAAO,QAAQ,OAAO,IAAE,QAAQ,QAAM,aAAa;AAAI;AAAC,SAAS,WAAW,cAAa,UAAS;AAAC,MAAG,OAAO,KAAK,QAAQ,EAAE,WAAS,EAAE;AAAO,MAAI,cAAY,WAAW,YAAY,IAAE,eAAa,SAAS,YAAY;AAAE,SAAO,KAAK,WAAW,EAAE,QAAQ,SAAK;AAAC,WAAO,YAAU,OAAO,YAAY,GAAG;AAAA,EAAE,CAAC,GAAE,OAAO,OAAO,aAAY,QAAQ;AAAE;AAAC,SAAS,SAAS,cAAa,eAAc;AAAC,gBAAc,QAAQ,GAAE,IAAI,IAAI,aAAa,KAAG,IAAI,OAAO,aAAa;AAAE;AAAC,SAAS,6BAA6B,SAAQ;AAAC,SAAO,OAAO,WAAS,aAAW,EAAC,QAAO,SAAQ,MAAK,QAAQ,KAAI,IAAE;AAAO;AAAC,SAAS,QAAQ,UAAS,YAAW;AAAC,MAAI,QAAM;AAAS,SAAO,UAAQ,OAAK,OAAK,OAAO,SAAO,aAAW,QAAM,aAAW,EAAC,GAAG,6BAA6B,KAAK,GAAE,YAAW,EAAC,GAAG,MAAM,cAAY,CAAC,GAAE,OAAM,WAAU,EAAC,IAAE,EAAC,SAAQ;AAAC,WAAO,EAAE,KAAK;AAAA,EAAC,EAAC;AAAC;AAAC,SAAS,cAAc,SAAQ,YAAW;AAAC,SAAO,WAAW,OAAO,CAAC,WAAU,cAAY,aAAS;AAAC,QAAI,OAAM,iBAAe,UAAU,YAAQ;AAAC,UAAI,sBAAgB,+CAA2B,MAAM;AAAE,aAAO,WAAS,gBAAgB,OAAK,OAAO,OAAO,QAAQ,MAAK,gBAAgB,IAAI,IAAG,QAAM,UAAU,EAAC,GAAG,SAAQ,GAAG,gBAAe,CAAC,GAAE;AAAA,IAAK,GAAE,OAAO;AAAE,WAAO,UAAQ,QAAM,UAAU,OAAO,IAAG,mBAAiB,QAAM,QAAM,QAAQ,gBAAe,MAAI,EAAE,KAAK,CAAC;AAAA,EAAC,GAAE,aAAS,QAAQ,QAAQ,OAAO,CAAC,CAAC;AAAC;AAAC,IAAI,QAAM,aAAS,OAAM,WAAU,aAAW,cAAY,QAAQ,kBAAgB,MAAI,MAAI,EAAE,WAAU,SAAS,OAAM,SAAS,KAAK,IAAG,MAAM,QAAQ,eAAe,GAAE,QAAQ;AAAQ,IAAI,aAAW,EAAC,UAAS,OAAM;", "names": []}