#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/code/work/components/node_modules/.pnpm/vitest@3.2.4_@types+node@22.16.0_jiti@2.4.2_jsdom@26.1.0/node_modules/vitest/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/vitest@3.2.4_@types+node@22.16.0_jiti@2.4.2_jsdom@26.1.0/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/code/work/components/node_modules/.pnpm/vitest@3.2.4_@types+node@22.16.0_jiti@2.4.2_jsdom@26.1.0/node_modules/vitest/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/vitest@3.2.4_@types+node@22.16.0_jiti@2.4.2_jsdom@26.1.0/node_modules:/mnt/e/code/work/components/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/vitest@3.2.4_@types+node@22.16.0_jiti@2.4.2_jsdom@26.1.0/node_modules/vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/vitest@3.2.4_@types+node@22.16.0_jiti@2.4.2_jsdom@26.1.0/node_modules/vitest/vitest.mjs" "$@"
fi
