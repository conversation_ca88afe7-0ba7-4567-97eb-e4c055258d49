import {
  entry_preview_exports,
  renderToCanvas,
  setup
} from "./chunk-GZERRZES.js";
import "./chunk-APLEHN7L.js";
import {
  h
} from "./chunk-Y4H6EBV6.js";
import {
  require_global
} from "./chunk-EETXLJ6A.js";
import {
  require_preview_api
} from "./chunk-RF4OLZDA.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/@storybook+vue3@8.6.14_storybook@8.6.14_vue@3.5.17/node_modules/@storybook/vue3/dist/index.mjs
var import_global = __toESM(require_global(), 1);
var import_preview_api = __toESM(require_preview_api(), 1);
var { window: globalWindow } = import_global.global;
globalWindow.STORYBOOK_ENV = "vue3";
globalWindow.PLUGINS_SETUP_FUNCTIONS ||= /* @__PURE__ */ new Set();
function setProjectAnnotations(projectAnnotations) {
  return (0, import_preview_api.setDefaultProjectAnnotations)(vueProjectAnnotations), (0, import_preview_api.setProjectAnnotations)(projectAnnotations);
}
var vueProjectAnnotations = { ...entry_preview_exports, renderToCanvas: (renderContext, canvasElement) => {
  if (renderContext.storyContext.testingLibraryRender == null) return renderToCanvas(renderContext, canvasElement);
  let { storyFn, storyContext: { testingLibraryRender: render } } = renderContext, { unmount } = render(storyFn(), { container: canvasElement });
  return unmount;
} };
function composeStory(story, componentAnnotations, projectAnnotations, exportsName) {
  let composedStory = (0, import_preview_api.composeStory)(story, componentAnnotations, projectAnnotations, globalThis.globalProjectAnnotations ?? vueProjectAnnotations, exportsName), renderable = (...args) => h(composedStory(...args));
  return Object.assign(renderable, composedStory), renderable;
}
function composeStories(csfExports, projectAnnotations) {
  return (0, import_preview_api.composeStories)(csfExports, projectAnnotations, composeStory);
}
try {
  module?.hot?.decline && module.hot.decline();
} catch {
}
export {
  composeStories,
  composeStory,
  setProjectAnnotations,
  setup,
  vueProjectAnnotations
};
//# sourceMappingURL=@storybook_vue3.js.map
