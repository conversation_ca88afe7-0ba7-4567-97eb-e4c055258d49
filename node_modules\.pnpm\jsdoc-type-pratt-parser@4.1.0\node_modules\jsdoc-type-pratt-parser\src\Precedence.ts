// higher precedence = higher importance
export enum Precedence {
  ALL,
  PARAMETER_LIST,
  OBJECT,
  KEY_VALUE,
  INDEX_BRACKETS,
  UNION,
  INTERSECTION,
  PREFIX,
  INFIX,
  TUPLE,
  <PERSON><PERSON><PERSON><PERSON>,
  OPTIONAL,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  KEY_OF_TYPE_OF,
  FUNCTION,
  <PERSON><PERSON><PERSON>,
  ARRAY_BRACKETS,
  GENERIC,
  NAME_PATH,
  PARENTHESIS,
  SPECIAL_TYPES
}
