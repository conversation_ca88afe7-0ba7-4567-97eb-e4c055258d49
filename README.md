# Components Monorepo

一个基于 pnpm workspace 的 Vue3 组件库 monorepo 项目，包含表格组件库和 Storybook 文档。

## 项目结构

```
components-monorepo/
├── packages/
│   ├── table/                 # Vue3 表格组件库
│   │   ├── src/
│   │   │   ├── components/    # 组件源码
│   │   │   ├── types/         # TypeScript 类型定义
│   │   │   ├── index.ts       # 库入口文件
│   │   │   └── style.css      # 样式文件
│   │   ├── package.json
│   │   └── vite.config.ts     # Vite 库模式配置
│   └── storybook/             # Storybook 文档项目
│       ├── .storybook/        # Storybook 配置
│       ├── src/               # Stories 文件
│       └── package.json
├── package.json               # 根项目配置
├── pnpm-workspace.yaml        # pnpm workspace 配置
└── .augment                   # Augment 规则文件
```

## 技术栈

- **包管理器**: pnpm
- **构建工具**: Vite
- **前端框架**: Vue3 + TypeScript
- **样式**: TailwindCSS
- **文档**: Storybook
- **开发工具**: ESLint + Prettier

## 快速开始

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
# 启动 Storybook 开发服务器
pnpm storybook

# 启动 table 库开发模式
pnpm --filter table dev
```

### 构建

```bash
# 构建所有项目
pnpm build

# 只构建 table 库
pnpm build:table

# 构建 Storybook 静态文件
pnpm build:storybook
```

## 组件库使用

### 安装

在你的项目中安装 table 组件库：

```bash
# 如果是在同一个 workspace 中
pnpm add @components/table

# 或者从构建产物中引用
```

### 使用示例

```vue
<template>
  <DataTable 
    :columns="columns" 
    :data="data" 
    :pagination="true"
    :page-size="10"
  />
</template>

<script setup lang="ts">
import { DataTable } from '@components/table'
import type { TableColumn, TableData } from '@components/table'

const columns: TableColumn[] = [
  { key: 'id', title: 'ID', sortable: true },
  { key: 'name', title: '姓名', sortable: true },
  { key: 'age', title: '年龄', align: 'center' },
]

const data: TableData[] = [
  { id: 1, name: '张三', age: 28 },
  { id: 2, name: '李四', age: 32 },
]
</script>
```

## 开发指南

### 添加新组件

1. 在 `packages/table/src/components/` 创建组件文件
2. 在 `packages/table/src/types/` 添加类型定义
3. 在 `packages/table/src/index.ts` 导出组件
4. 在 `packages/storybook/src/` 创建对应的 Stories

### 运行测试

```bash
# 运行类型检查
pnpm type-check

# 运行 linting
pnpm lint
```

## 脚本命令

- `pnpm dev` - 启动所有项目的开发模式
- `pnpm build` - 构建所有项目
- `pnpm storybook` - 启动 Storybook 开发服务器
- `pnpm type-check` - 运行 TypeScript 类型检查
- `pnpm lint` - 运行代码检查
- `pnpm clean` - 清理构建产物

## 许可证

MIT
