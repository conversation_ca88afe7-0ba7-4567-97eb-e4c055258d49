@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\which@5.0.0\node_modules\which\bin\node_modules;E:\code\work\components\node_modules\.pnpm\which@5.0.0\node_modules\which\node_modules;E:\code\work\components\node_modules\.pnpm\which@5.0.0\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\which@5.0.0\node_modules\which\bin\node_modules;E:\code\work\components\node_modules\.pnpm\which@5.0.0\node_modules\which\node_modules;E:\code\work\components\node_modules\.pnpm\which@5.0.0\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\which@5.0.0\node_modules\which\bin\which.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\which@5.0.0\node_modules\which\bin\which.js" %*
)
