import { keyboardKey } from '../system/keyboard';
/**
 * Parse key defintions per `keyboardMap`
 *
 * Keys can be referenced by `{key}` or `{special}` as well as physical locations per `[code]`.
 * Everything else will be interpreted as a typed character - e.g. `a`.
 * Brackets `{` and `[` can be escaped by doubling - e.g. `foo[[bar` translates to `foo[bar`.
 * Keeping the key pressed can be written as `{key>}`.
 * When keeping the key pressed you can choose how long (how many keydown and keypress) the key is pressed `{key>3}`.
 * You can then release the key per `{key>3/}` or keep it pressed and continue with the next key.
 */
export declare function parseKeyDef(keyboardMap: keyboardKey[], text: string): {
    keyDef: keyboardKey;
    releasePrevious: boolean;
    releaseSelf: boolean;
    repeat: number;
}[];
