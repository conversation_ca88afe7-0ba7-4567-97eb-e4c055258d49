{"author": "<PERSON> <<EMAIL>>", "name": "ast-types", "version": "0.16.1", "description": "Esprima-compatible implementation of the Mozilla JS Parser API", "keywords": ["ast", "abstract syntax tree", "hierarchy", "mozilla", "spidermonkey", "parser api", "esprima", "types", "type system", "type checking", "dynamic types", "parsing", "transformation", "syntax"], "homepage": "http://github.com/benjamn/ast-types", "repository": {"type": "git", "url": "git://github.com/benjamn/ast-types.git"}, "license": "MIT", "main": "lib/main.js", "types": "lib/main.d.ts", "exports": {".": "./lib/main.js", "./lib/*": "./lib/*.js", "./lib/*.js": "./lib/*.js", "./*": "./lib/*.js", "./*.js": "./lib/*.js"}, "scripts": {"gen": "ts-node --transpile-only script/gen-types.ts", "test": "npm run gen && npm run build && script/run-tests.sh", "clean": "rimraf lib/", "build": "tsc", "prepare": "npm run clean && npm run build"}, "dependencies": {"tslib": "^2.0.1"}, "devDependencies": {"@babel/parser": "7.20.5", "@babel/types": "7.20.5", "@types/esprima": "4.0.3", "@types/glob": "8.0.0", "@types/mocha": "9.0.0", "espree": "9.4.1", "esprima": "4.0.1", "esprima-fb": "15001.1001.0-dev-harmony-fb", "flow-parser": "0.195.2", "glob": "8.0.3", "mocha": "^9.1.3", "recast": "^0.23.0", "reify": "0.20.12", "rimraf": "3.0.2", "ts-node": "10.9.1", "typescript": "4.9.4"}, "engines": {"node": ">=4"}}