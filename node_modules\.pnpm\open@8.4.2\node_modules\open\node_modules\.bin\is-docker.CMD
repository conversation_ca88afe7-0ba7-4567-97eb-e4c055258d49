@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\is-docker@2.2.1\node_modules\is-docker\node_modules;E:\code\work\components\node_modules\.pnpm\is-docker@2.2.1\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\is-docker@2.2.1\node_modules\is-docker\node_modules;E:\code\work\components\node_modules\.pnpm\is-docker@2.2.1\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\is-docker@2.2.1\node_modules\is-docker\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\is-docker@2.2.1\node_modules\is-docker\cli.js" %*
)
