try{
(()=>{var O=__STORYBOOK_API__,{ActiveTabs:h,Consumer:T,ManagerContext:A,Provider:g,RequestResponseError:w,addons:l,combineParameters:E,controlOrMetaKey:f,controlOrMetaSymbol:v,eventMatchesShortcut:x,eventToShortcut:P,experimental_MockUniversalStore:C,experimental_UniversalStore:M,experimental_requestResponse:R,experimental_useUniversalStore:U,isMacLike:L,isShortcutTaken:B,keyToSymbol:D,merge:I,mockChannel:K,optionOrAltSymbol:N,shortcutMatchesShortcut:G,shortcutToHumanString:V,types:Y,useAddonState:q,useArgTypes:F,useArgs:H,useChannel:j,useGlobalTypes:z,useGlobals:J,useParameter:Q,useSharedState:W,useStoryPrepared:X,useStorybookApi:Z,useStorybookState:$}=__STORYBOOK_API__;var u=(()=>{let e;return typeof window<"u"?e=window:typeof globalThis<"u"?e=globalThis:typeof window<"u"?e=window:typeof self<"u"?e=self:e={},e})(),p="tag-filters",c="static-filter";l.register(p,e=>{let a=Object.entries(u.TAGS_OPTIONS??{}).reduce((o,t)=>{let[s,m]=t;return m.excludeFromSidebar&&(o[s]=!0),o},{});e.experimental_setFilter(c,o=>{let t=o.tags??[];return(t.includes("dev")||o.type==="docs")&&t.filter(s=>a[s]).length===0})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
