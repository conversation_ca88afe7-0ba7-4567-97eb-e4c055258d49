{"name": "@components/storybook", "version": "1.0.0", "private": true, "type": "module", "scripts": {"storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "serve-storybook": "npx http-server storybook-static", "clean": "<PERSON><PERSON><PERSON> storybook-static"}, "dependencies": {"@components/table": "workspace:*", "vue": "^3.5.17"}, "devDependencies": {"@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/test": "^8.4.7", "@storybook/vue3": "^8.4.7", "@storybook/vue3-vite": "^8.4.7", "@types/node": "^22.15.32", "rimraf": "^5.0.5", "storybook": "^8.4.7", "typescript": "~5.8.0", "vite": "^7.0.0"}, "volta": {"node": "22.17.0"}}