import {
  require_preview_api
} from "./chunk-TCB5WPCR.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/@storybook+core@8.6.14_storybook@8.6.14/node_modules/@storybook/core/dist/csf/index.js
var import_preview_api = __toESM(require_preview_api());
var b = Object.create;
var f = Object.defineProperty;
var v = Object.getOwnPropertyDescriptor;
var P = Object.getOwnPropertyNames;
var O = Object.getPrototypeOf;
var _ = Object.prototype.hasOwnProperty;
var s = (e, r) => f(e, "name", { value: r, configurable: true });
var $ = (e, r) => () => (r || e((r = { exports: {} }).exports, r), r.exports);
var j = (e, r, t, n) => {
  if (r && typeof r == "object" || typeof r == "function")
    for (let a of P(r))
      !_.call(e, a) && a !== t && f(e, a, { get: () => r[a], enumerable: !(n = v(r, a)) || n.enumerable });
  return e;
};
var C = (e, r, t) => (t = e != null ? b(O(e)) : {}, j(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  r || !e || !e.__esModule ? f(t, "default", { value: e, enumerable: true }) : t,
  e
));
var T = $((g) => {
  Object.defineProperty(g, "__esModule", { value: true }), g.isEqual = /* @__PURE__ */ function() {
    var e = Object.prototype.toString, r = Object.getPrototypeOf, t = Object.getOwnPropertySymbols ? function(n) {
      return Object.keys(n).concat(Object.getOwnPropertySymbols(n));
    } : Object.keys;
    return function(n, a) {
      return s(function d(o, i, p) {
        var c, u, l, m = e.call(o), h = e.call(i);
        if (o === i) return true;
        if (o == null || i == null) return false;
        if (p.indexOf(o) > -1 && p.indexOf(i) > -1) return true;
        if (p.push(o, i), m != h || (c = t(o), u = t(i), c.length != u.length || c.some(function(A) {
          return !d(o[A], i[A], p);
        }))) return false;
        switch (m.slice(8, -1)) {
          case "Symbol":
            return o.valueOf() == i.valueOf();
          case "Date":
          case "Number":
            return +o == +i || +o != +o && +i != +i;
          case "RegExp":
          case "Function":
          case "String":
          case "Boolean":
            return "" + o == "" + i;
          case "Set":
          case "Map":
            c = o.entries(), u = i.entries();
            do
              if (!d((l = c.next()).value, u.next().value, p)) return false;
            while (!l.done);
            return true;
          case "ArrayBuffer":
            o = new Uint8Array(o), i = new Uint8Array(i);
          case "DataView":
            o = new Uint8Array(o.buffer), i = new Uint8Array(i.buffer);
          case "Float32Array":
          case "Float64Array":
          case "Int8Array":
          case "Int16Array":
          case "Int32Array":
          case "Uint8Array":
          case "Uint16Array":
          case "Uint32Array":
          case "Uint8ClampedArray":
          case "Arguments":
          case "Array":
            if (o.length != i.length) return false;
            for (l = 0; l < o.length; l++) if ((l in o || l in i) && (l in o != l in i || !d(o[l], i[l], p))) return false;
            return true;
          case "Object":
            return d(r(o), r(i), p);
          default:
            return false;
        }
      }, "n")(n, a, []);
    };
  }();
});
function R(e) {
  return e.replace(/_/g, " ").replace(/-/g, " ").replace(/\./g, " ").replace(/([^\n])([A-Z])([a-z])/g, (r, t, n, a) => `${t} ${n}${a}`).replace(
    /([a-z])([A-Z])/g,
    (r, t, n) => `${t} ${n}`
  ).replace(/([a-z])([0-9])/gi, (r, t, n) => `${t} ${n}`).replace(/([0-9])([a-z])/gi, (r, t, n) => `${t} ${n}`).replace(/(\s|^)(\w)/g, (r, t, n) => `${t}${n.toUpperCase()}`).replace(/ +/g, " ").trim();
}
s(R, "toStartCaseStr");
var y = C(T(), 1);
var x = s((e) => e.map((r) => typeof r < "u").filter(Boolean).length, "count");
var E = s((e, r) => {
  let { exists: t, eq: n, neq: a, truthy: d } = e;
  if (x([t, n, a, d]) > 1)
    throw new Error(`Invalid conditional test ${JSON.stringify({ exists: t, eq: n, neq: a })}`);
  if (typeof n < "u")
    return (0, y.isEqual)(r, n);
  if (typeof a < "u")
    return !(0, y.isEqual)(r, a);
  if (typeof t < "u") {
    let i = typeof r < "u";
    return t ? i : !i;
  }
  return (typeof d > "u" ? true : d) ? !!r : !r;
}, "testValue");
var z = s((e, r, t) => {
  if (!e.if)
    return true;
  let { arg: n, global: a } = e.if;
  if (x([n, a]) !== 1)
    throw new Error(`Invalid conditional value ${JSON.stringify({ arg: n, global: a })}`);
  let d = n ? r[n] : t[a];
  return E(e.if, d);
}, "includeConditionalArg");
function L(e) {
  let r, t = {
    _tag: "Preview",
    input: e,
    get composed() {
      if (r)
        return r;
      let { addons: n, ...a } = e;
      return r = (0, import_preview_api.normalizeProjectAnnotations)((0, import_preview_api.composeConfigs)([...n ?? [], a])), r;
    },
    meta(n) {
      return I(n, this);
    }
  };
  return globalThis.globalProjectAnnotations = t.composed, t;
}
s(L, "__definePreview");
function W(e) {
  return e != null && typeof e == "object" && "_tag" in e && e?._tag === "Preview";
}
s(W, "isPreview");
function H(e) {
  return e != null && typeof e == "object" && "_tag" in e && e?._tag === "Meta";
}
s(H, "isMeta");
function I(e, r) {
  return {
    _tag: "Meta",
    input: e,
    preview: r,
    get composed() {
      throw new Error("Not implemented");
    },
    story(t) {
      return U(t, this);
    }
  };
}
s(I, "defineMeta");
function U(e, r) {
  return {
    _tag: "Story",
    input: e,
    meta: r,
    get composed() {
      throw new Error("Not implemented");
    }
  };
}
s(U, "defineStory");
function K(e) {
  return e != null && typeof e == "object" && "_tag" in e && e?._tag === "Story";
}
s(K, "isStory");
var D = s((e) => e.toLowerCase().replace(/[ ’–—―′¿'`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi, "-").replace(
  /-+/g,
  "-"
).replace(/^-+/, "").replace(/-+$/, ""), "sanitize");
var w = s((e, r) => {
  let t = D(e);
  if (t === "")
    throw new Error(`Invalid ${r} '${e}', must include alphanumeric characters`);
  return t;
}, "sanitizeSafe");
var ee = s((e, r) => `${w(e, "kind")}${r ? `--${w(r, "name")}` : ""}`, "toId");
var re = s((e) => R(
  e
), "storyNameFromExport");
function S(e, r) {
  return Array.isArray(r) ? r.includes(e) : e.match(r);
}
s(S, "matches");
function te(e, { includeStories: r, excludeStories: t }) {
  return (
    // https://babeljs.io/docs/en/babel-plugin-transform-modules-commonjs
    e !== "__esModule" && (!r || S(e, r)) && (!t || !S(e, t))
  );
}
s(te, "isExportStory");
var ne = s((e, { rootSeparator: r, groupSeparator: t }) => {
  let [n, a] = e.split(r, 2), d = (a || e).split(t).filter((o) => !!o);
  return {
    root: a ? n : null,
    groups: d
  };
}, "parseKind");
var oe = s((...e) => {
  let r = e.reduce((t, n) => (n.startsWith("!") ? t.delete(n.slice(1)) : t.add(n), t), /* @__PURE__ */ new Set());
  return Array.from(r);
}, "combineTags");

export {
  z,
  L,
  W,
  H,
  K,
  D,
  ee,
  re,
  te,
  ne,
  oe
};
//# sourceMappingURL=chunk-IC4LA6UY.js.map
