async function click(element) {
    const pointerIn = [];
    if (!this.config.skipHover) {
        pointerIn.push({
            target: element
        });
    }
    pointerIn.push({
        keys: '[<PERSON>Left]',
        target: element
    });
    return this.pointer(pointerIn);
}
async function dblClick(element) {
    return this.pointer([
        {
            target: element
        },
        '[<PERSON>Left][MouseLeft]'
    ]);
}
async function tripleClick(element) {
    return this.pointer([
        {
            target: element
        },
        '[<PERSON>Left][<PERSON>Left][MouseLeft]'
    ]);
}

export { click, dblClick, tripleClick };
