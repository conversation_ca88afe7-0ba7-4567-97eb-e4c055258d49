import { defineComponent as S, ref as g, computed as h, createElementBlock as s, openBlock as a, createCommentVNode as v, createElementVNode as n, normalizeClass as m, Fragment as b, renderList as k, normalizeStyle as w, createTextVNode as D, toDisplayString as y, createBlock as x, resolveDynamicComponent as V } from "vue";
const H = { class: "data-table-wrapper" }, T = {
  key: 0,
  class: "flex justify-center items-center py-8"
}, j = { key: 0 }, M = ["onClick"], N = { class: "flex items-center" }, $ = {
  key: 0,
  class: "ml-1"
}, A = { key: 1 }, E = { key: 1 }, O = { key: 0 }, P = ["colspan"], F = {
  key: 2,
  class: "flex justify-center mt-4"
}, I = { class: "flex space-x-2" }, K = ["disabled"], L = { class: "px-3 py-1 border rounded bg-blue-50 text-blue-600" }, q = ["disabled"], G = /* @__PURE__ */ S({
  __name: "DataTable",
  props: {
    columns: {},
    data: {},
    loading: { type: Boolean, default: !1 },
    pagination: { type: Boolean, default: !1 },
    pageSize: { default: 10 },
    showHeader: { type: Boolean, default: !0 },
    bordered: { type: Boolean, default: !0 },
    striped: { type: Boolean, default: !1 },
    hoverable: { type: Boolean, default: !0 }
  },
  setup(c) {
    const r = c, l = g(1), u = g(""), p = g("asc"), f = h(() => r.pagination ? Math.ceil(r.data.length / r.pageSize) : 1), _ = h(() => u.value ? [...r.data].sort((t, o) => {
      const e = t[u.value], i = o[u.value];
      return e < i ? p.value === "asc" ? -1 : 1 : e > i ? p.value === "asc" ? 1 : -1 : 0;
    }) : r.data), z = h(() => {
      if (!r.pagination) return _.value;
      const t = (l.value - 1) * r.pageSize, o = t + r.pageSize;
      return _.value.slice(t, o);
    }), C = (t) => {
      u.value === t ? p.value = p.value === "asc" ? "desc" : "asc" : (u.value = t, p.value = "asc"), l.value = 1;
    };
    return (t, o) => (a(), s("div", H, [
      t.loading ? (a(), s("div", T, o[2] || (o[2] = [
        n("div", { class: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" }, null, -1),
        n("span", { class: "ml-2 text-gray-600" }, "加载中...", -1)
      ]))) : (a(), s("table", {
        key: 1,
        class: m([
          "data-table",
          {
            "border-0": !t.bordered,
            "data-table-striped": t.striped,
            "data-table-hoverable": t.hoverable
          }
        ])
      }, [
        t.showHeader ? (a(), s("thead", j, [
          n("tr", null, [
            (a(!0), s(b, null, k(t.columns, (e) => (a(), s("th", {
              key: e.key,
              style: w({ width: e.width, textAlign: e.align || "left" }),
              class: m({ "cursor-pointer select-none": e.sortable }),
              onClick: (i) => e.sortable && C(e.key)
            }, [
              n("div", N, [
                D(y(e.title) + " ", 1),
                e.sortable ? (a(), s("span", $, o[3] || (o[3] = [
                  n("svg", {
                    class: "w-4 h-4",
                    fill: "currentColor",
                    viewBox: "0 0 20 20"
                  }, [
                    n("path", { d: "M5 8l5-5 5 5H5zM5 12l5 5 5-5H5z" })
                  ], -1)
                ]))) : v("", !0)
              ])
            ], 14, M))), 128))
          ])
        ])) : v("", !0),
        n("tbody", null, [
          (a(!0), s(b, null, k(z.value, (e, i) => (a(), s("tr", { key: i }, [
            (a(!0), s(b, null, k(t.columns, (d) => (a(), s("td", {
              key: d.key,
              style: w({ textAlign: d.align || "left" })
            }, [
              d.render ? (a(), s(b, { key: 0 }, [
                typeof d.render(e[d.key], e, i) == "object" ? (a(), x(V(d.render(e[d.key], e, i)), { key: 0 })) : (a(), s("span", A, y(d.render(e[d.key], e, i)), 1))
              ], 64)) : (a(), s("span", E, y(e[d.key]), 1))
            ], 4))), 128))
          ]))), 128)),
          t.data.length === 0 ? (a(), s("tr", O, [
            n("td", {
              colspan: t.columns.length,
              class: "text-center py-8 text-gray-500"
            }, " 暂无数据 ", 8, P)
          ])) : v("", !0)
        ])
      ], 2)),
      t.pagination && t.data.length > t.pageSize ? (a(), s("div", F, [
        n("nav", I, [
          n("button", {
            onClick: o[0] || (o[0] = (e) => l.value > 1 && l.value--),
            disabled: l.value === 1,
            class: "px-3 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          }, " 上一页 ", 8, K),
          n("span", L, y(l.value) + " / " + y(f.value), 1),
          n("button", {
            onClick: o[1] || (o[1] = (e) => l.value < f.value && l.value++),
            disabled: l.value === f.value,
            class: "px-3 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          }, " 下一页 ", 8, q)
        ])
      ])) : v("", !0)
    ]));
  }
}), J = (c, r) => {
  const l = c.__vccOpts || c;
  for (const [u, p] of r)
    l[u] = p;
  return l;
}, B = /* @__PURE__ */ J(G, [["__scopeId", "data-v-d5bd30d8"]]);
function Q(c) {
  c.component("DataTable", B);
}
const U = {
  install: Q,
  DataTable: B
};
export {
  B as DataTable,
  U as default,
  Q as install
};
