import "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/@storybook+addon-viewport@8.6.14_storybook@8.6.14/node_modules/@storybook/addon-viewport/dist/preview.mjs
var PARAM_KEY = "viewport";
var modern = { [PARAM_KEY]: { value: void 0, isRotated: false } };
var legacy = { viewport: "reset", viewportRotated: false };
var initialGlobals = globalThis.FEATURES?.viewportStoryGlobals ? modern : legacy;
export {
  initialGlobals
};
//# sourceMappingURL=@storybook_addon-essentials_viewport_preview.js.map
