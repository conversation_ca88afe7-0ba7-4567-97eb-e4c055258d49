import {
  __export
} from "./chunk-JNS2GCPK.js";
import "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/@storybook+addon-docs@8.6.14_@types+react@19.1.8_storybook@8.6.14/node_modules/@storybook/addon-docs/dist/chunk-PRSJUHPQ.mjs
var preview_exports = {};
__export(preview_exports, { parameters: () => parameters });
var excludeTags = Object.entries(globalThis.TAGS_OPTIONS ?? {}).reduce((acc, entry) => {
  let [tag, option] = entry;
  return option.excludeFromDocsStories && (acc[tag] = true), acc;
}, {});
var parameters = { docs: { renderer: async () => {
  let { DocsRenderer } = await import("./DocsRenderer-CFRXHY34-T4FG3YHK.js");
  return new DocsRenderer();
}, stories: { filter: (story) => (story.tags || []).filter((tag) => excludeTags[tag]).length === 0 && !story.parameters.docs?.disable } } };
export {
  parameters
};
//# sourceMappingURL=@storybook_addon-essentials_docs_preview.js.map
