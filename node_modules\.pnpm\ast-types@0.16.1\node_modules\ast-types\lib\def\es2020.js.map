{"version": 3, "file": "es2020.js", "sourceRoot": "", "sources": ["../../src/def/es2020.ts"], "names": [], "mappings": ";;;AACA,sEAA8C;AAC9C,4DAAiC;AACjC,2DAAmC;AACnC,0DAAgE;AAEhE,mBAAyB,IAAU;IACjC,6EAA6E;IAC7E,6CAA6C;IAC7C,IAAI,CAAC,GAAG,CAAC,gBAAY,CAAC,CAAC;IAEvB,IAAI,CAAC,GAAG,CAAC,gBAAS,CAAC,CAAC;IAEpB,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAW,CAAC,CAAC;IACpC,IAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;IAC3B,IAAM,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;IAEzB,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAY,CAAC,CAAC;IACtC,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;IAEjC,GAAG,CAAC,kBAAkB,CAAC;SACpB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,QAAQ,CAAC;SACf,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IAEtC,GAAG,CAAC,sBAAsB,CAAC;SACxB,KAAK,CAAC,aAAa,CAAC;SACpB,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC;SAC3B,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;SAC/B,KAAK,CAAC,UAAU,EAAE,EAAE,CACnB,GAAG,CAAC,YAAY,CAAC,EACjB,IAAI,EACJ,KAAK,CAAC,CACP,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAEvB,oBAAoB;IACpB,GAAG,CAAC,cAAc,CAAC;SAChB,KAAK,CAAC,MAAM,CAAC;SACb,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAEjD,GAAG,CAAC,gBAAgB,CAAC;SAClB,KAAK,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;IAEvC,GAAG,CAAC,kBAAkB,CAAC;SACpB,KAAK,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;IAEvC,GAAG,CAAC,iBAAiB,CAAC;SACnB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,YAAY,CAAC;SACnB,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;IAE5C,GAAG,CAAC,wBAAwB,CAAC;SAC1B,KAAK,CAAC,gBAAgB,CAAC;SACvB,KAAK,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;SACxC,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAEhD,mFAAmF;IACnF,GAAG,CAAC,0BAA0B,CAAC;SAC5B,KAAK,CAAC,kBAAkB,CAAC;SACzB,KAAK,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;SACnD,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAClD,CAAC;AAvDD,4BAuDC;AAAA,CAAC;AAEF,IAAA,8BAAqB,EAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,CAAC"}