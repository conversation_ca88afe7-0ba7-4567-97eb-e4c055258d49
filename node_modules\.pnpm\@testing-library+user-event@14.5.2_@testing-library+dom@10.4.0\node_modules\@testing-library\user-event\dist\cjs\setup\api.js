'use strict';

var click = require('../convenience/click.js');
var hover = require('../convenience/hover.js');
var tab = require('../convenience/tab.js');
var index = require('../keyboard/index.js');
var copy = require('../clipboard/copy.js');
var cut = require('../clipboard/cut.js');
var paste = require('../clipboard/paste.js');
var index$1 = require('../pointer/index.js');
var clear = require('../utility/clear.js');
var selectOptions = require('../utility/selectOptions.js');
var type = require('../utility/type.js');
var upload = require('../utility/upload.js');

const userEventApi = {
    click: click.click,
    dblClick: click.dblClick,
    tripleClick: click.tripleClick,
    hover: hover.hover,
    unhover: hover.unhover,
    tab: tab.tab,
    keyboard: index.keyboard,
    copy: copy.copy,
    cut: cut.cut,
    paste: paste.paste,
    pointer: index$1.pointer,
    clear: clear.clear,
    deselectOptions: selectOptions.deselectOptions,
    selectOptions: selectOptions.selectOptions,
    type: type.type,
    upload: upload.upload
};

exports.userEventApi = userEventApi;
