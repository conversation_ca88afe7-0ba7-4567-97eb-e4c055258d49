{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "mnqZbFhQCKdPJVnFQ3WQv", "sessionId": "GdgPsEgYKDKnCLQlZMJTE", "payload": {"eventType": "dev"}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.17.0", "cliVersion": "8.6.14"}}, "timestamp": 1751511424989}, "version-update": {"body": {"eventType": "version-update", "eventId": "msclgYvpyDyB7H7z0J5FK", "sessionId": "GdgPsEgYKDKnCLQlZMJTE", "metadata": {"generatedAt": 1751507861340, "userSince": 1751507398030, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "packageManager": {"type": "pnpm", "version": "8.15.0", "agent": "pnpm"}, "typescriptOptions": {"check": false, "reactDocgen": "react-docgen-typescript", "reactDocgenTypescriptOptions": {"shouldExtractLiteralValuesFromEnum": true}}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/vue3-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/vue3", "storybookVersion": "8.6.14", "storybookVersionSpecifier": "^8.4.7", "language": "typescript", "storybookPackages": {"@storybook/blocks": {"version": "8.6.14"}, "@storybook/test": {"version": "8.6.14"}, "@storybook/vue3": {"version": "8.6.14"}, "@storybook/vue3-vite": {"version": "8.6.14"}, "storybook": {"version": "8.6.14"}}, "addons": {"@storybook/addon-links": {"version": "8.6.14"}, "@storybook/addon-essentials": {"version": "8.6.14"}, "@storybook/addon-interactions": {"version": "8.6.14"}}}, "payload": {}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.17.0", "cliVersion": "8.6.14"}}, "timestamp": 1751507864662}, "dev": {"body": {"eventType": "dev", "eventId": "1RwdHbRbzt2mgxXDHfNnW", "sessionId": "GdgPsEgYKDKnCLQlZMJTE", "metadata": {"generatedAt": 1751511431543, "userSince": 1751507398030, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "monorepo": "Workspaces", "packageManager": {"type": "pnpm", "version": "8.15.0", "agent": "pnpm"}, "typescriptOptions": {"check": false, "reactDocgen": "react-docgen-typescript", "reactDocgenTypescriptOptions": {"shouldExtractLiteralValuesFromEnum": true}}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/vue3-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/vue3", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "8.6.14", "storybookVersionSpecifier": "^8.4.7", "language": "typescript", "storybookPackages": {"@storybook/blocks": {"version": "8.6.14"}, "@storybook/test": {"version": "8.6.14"}, "@storybook/vue3": {"version": "8.6.14"}, "@storybook/vue3-vite": {"version": "8.6.14"}, "storybook": {"version": "8.6.14"}}, "addons": {"@storybook/addon-links": {"version": "8.6.14"}, "@storybook/addon-essentials": {"version": "8.6.14"}, "@storybook/addon-interactions": {"version": "8.6.14"}}}, "payload": {"versionStatus": "cached", "storyIndex": {"storyCount": 7, "componentCount": 1, "pageStoryCount": 0, "playStoryCount": 0, "autodocsCount": 1, "mdxCount": 0, "exampleStoryCount": 0, "exampleDocsCount": 0, "onboardingStoryCount": 0, "onboardingDocsCount": 0, "version": 5}, "storyStats": {"factory": 0, "play": 0, "render": 0, "loaders": 0, "beforeEach": 0, "globals": 0, "tags": 0, "storyFn": 0, "mount": 0, "moduleMock": 0}}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.17.0", "cliVersion": "8.6.14"}}, "timestamp": 1751511432513}}}