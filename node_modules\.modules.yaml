hoistPattern:
  - '*'
hoistedDependencies:
  /@adobe/css-tools/4.4.3:
    '@adobe/css-tools': private
  /@alloc/quick-lru/5.2.0:
    '@alloc/quick-lru': private
  /@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  /@antfu/utils/0.7.10:
    '@antfu/utils': private
  /@asamuzakjp/css-color/3.2.0:
    '@asamuzakjp/css-color': private
  /@babel/code-frame/7.27.1:
    '@babel/code-frame': private
  /@babel/compat-data/7.28.0:
    '@babel/compat-data': private
  /@babel/core/7.28.0:
    '@babel/core': private
  /@babel/generator/7.28.0:
    '@babel/generator': private
  /@babel/helper-annotate-as-pure/7.27.3:
    '@babel/helper-annotate-as-pure': private
  /@babel/helper-compilation-targets/7.27.2:
    '@babel/helper-compilation-targets': private
  /@babel/helper-create-class-features-plugin/7.27.1(@babel/core@7.28.0):
    '@babel/helper-create-class-features-plugin': private
  /@babel/helper-globals/7.28.0:
    '@babel/helper-globals': private
  /@babel/helper-member-expression-to-functions/7.27.1:
    '@babel/helper-member-expression-to-functions': private
  /@babel/helper-module-imports/7.27.1:
    '@babel/helper-module-imports': private
  /@babel/helper-module-transforms/7.27.3(@babel/core@7.28.0):
    '@babel/helper-module-transforms': private
  /@babel/helper-optimise-call-expression/7.27.1:
    '@babel/helper-optimise-call-expression': private
  /@babel/helper-plugin-utils/7.27.1:
    '@babel/helper-plugin-utils': private
  /@babel/helper-replace-supers/7.27.1(@babel/core@7.28.0):
    '@babel/helper-replace-supers': private
  /@babel/helper-skip-transparent-expression-wrappers/7.27.1:
    '@babel/helper-skip-transparent-expression-wrappers': private
  /@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  /@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  /@babel/helper-validator-option/7.27.1:
    '@babel/helper-validator-option': private
  /@babel/helpers/7.27.6:
    '@babel/helpers': private
  /@babel/parser/7.28.0:
    '@babel/parser': private
  /@babel/plugin-proposal-decorators/7.28.0(@babel/core@7.28.0):
    '@babel/plugin-proposal-decorators': private
  /@babel/plugin-syntax-decorators/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-decorators': private
  /@babel/plugin-syntax-import-attributes/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-import-attributes': private
  /@babel/plugin-syntax-import-meta/7.10.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-import-meta': private
  /@babel/plugin-syntax-jsx/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-jsx': private
  /@babel/plugin-syntax-typescript/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-typescript': private
  /@babel/plugin-transform-typescript/7.28.0(@babel/core@7.28.0):
    '@babel/plugin-transform-typescript': private
  /@babel/runtime/7.27.6:
    '@babel/runtime': private
  /@babel/template/7.27.2:
    '@babel/template': private
  /@babel/traverse/7.28.0:
    '@babel/traverse': private
  /@babel/types/7.28.0:
    '@babel/types': private
  /@csstools/color-helpers/5.0.2:
    '@csstools/color-helpers': private
  /@csstools/css-calc/2.1.4(@csstools/css-parser-algorithms@3.0.5)(@csstools/css-tokenizer@3.0.4):
    '@csstools/css-calc': private
  /@csstools/css-color-parser/3.0.10(@csstools/css-parser-algorithms@3.0.5)(@csstools/css-tokenizer@3.0.4):
    '@csstools/css-color-parser': private
  /@csstools/css-parser-algorithms/3.0.5(@csstools/css-tokenizer@3.0.4):
    '@csstools/css-parser-algorithms': private
  /@csstools/css-tokenizer/3.0.4:
    '@csstools/css-tokenizer': private
  /@esbuild/aix-ppc64/0.25.5:
    '@esbuild/aix-ppc64': private
  /@esbuild/android-arm/0.25.5:
    '@esbuild/android-arm': private
  /@esbuild/android-arm64/0.25.5:
    '@esbuild/android-arm64': private
  /@esbuild/android-x64/0.25.5:
    '@esbuild/android-x64': private
  /@esbuild/darwin-arm64/0.25.5:
    '@esbuild/darwin-arm64': private
  /@esbuild/darwin-x64/0.25.5:
    '@esbuild/darwin-x64': private
  /@esbuild/freebsd-arm64/0.25.5:
    '@esbuild/freebsd-arm64': private
  /@esbuild/freebsd-x64/0.25.5:
    '@esbuild/freebsd-x64': private
  /@esbuild/linux-arm/0.25.5:
    '@esbuild/linux-arm': private
  /@esbuild/linux-arm64/0.25.5:
    '@esbuild/linux-arm64': private
  /@esbuild/linux-ia32/0.25.5:
    '@esbuild/linux-ia32': private
  /@esbuild/linux-loong64/0.25.5:
    '@esbuild/linux-loong64': private
  /@esbuild/linux-mips64el/0.25.5:
    '@esbuild/linux-mips64el': private
  /@esbuild/linux-ppc64/0.25.5:
    '@esbuild/linux-ppc64': private
  /@esbuild/linux-riscv64/0.25.5:
    '@esbuild/linux-riscv64': private
  /@esbuild/linux-s390x/0.25.5:
    '@esbuild/linux-s390x': private
  /@esbuild/linux-x64/0.25.5:
    '@esbuild/linux-x64': private
  /@esbuild/netbsd-arm64/0.25.5:
    '@esbuild/netbsd-arm64': private
  /@esbuild/netbsd-x64/0.25.5:
    '@esbuild/netbsd-x64': private
  /@esbuild/openbsd-arm64/0.25.5:
    '@esbuild/openbsd-arm64': private
  /@esbuild/openbsd-x64/0.25.5:
    '@esbuild/openbsd-x64': private
  /@esbuild/sunos-x64/0.25.5:
    '@esbuild/sunos-x64': private
  /@esbuild/win32-arm64/0.25.5:
    '@esbuild/win32-arm64': private
  /@esbuild/win32-ia32/0.25.5:
    '@esbuild/win32-ia32': private
  /@esbuild/win32-x64/0.25.5:
    '@esbuild/win32-x64': private
  /@eslint-community/eslint-utils/4.7.0(eslint@9.30.1):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/config-array/0.21.0:
    '@eslint/config-array': public
  /@eslint/config-helpers/0.3.0:
    '@eslint/config-helpers': public
  /@eslint/core/0.14.0:
    '@eslint/core': public
  /@eslint/eslintrc/3.3.1:
    '@eslint/eslintrc': public
  /@eslint/js/9.30.1:
    '@eslint/js': public
  /@eslint/object-schema/2.1.6:
    '@eslint/object-schema': public
  /@eslint/plugin-kit/0.3.3:
    '@eslint/plugin-kit': public
  /@humanfs/core/0.19.1:
    '@humanfs/core': private
  /@humanfs/node/0.16.6:
    '@humanfs/node': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/retry/0.4.3:
    '@humanwhocodes/retry': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@jridgewell/gen-mapping/0.3.12:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/sourcemap-codec/1.5.4:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.29:
    '@jridgewell/trace-mapping': private
  /@mdx-js/react/3.1.0(@types/react@19.1.8)(react@19.1.0):
    '@mdx-js/react': private
  /@microsoft/api-extractor-model/7.28.13(@types/node@22.16.0):
    '@microsoft/api-extractor-model': private
  /@microsoft/api-extractor/7.43.0(@types/node@22.16.0):
    '@microsoft/api-extractor': private
  /@microsoft/tsdoc-config/0.16.2:
    '@microsoft/tsdoc-config': private
  /@microsoft/tsdoc/0.14.2:
    '@microsoft/tsdoc': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@one-ini/wasm/0.1.1:
    '@one-ini/wasm': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@pkgr/core/0.2.7:
    '@pkgr/core': private
  /@playwright/test/1.53.2:
    '@playwright/test': private
  /@polka/url/1.0.0-next.29:
    '@polka/url': private
  /@rolldown/pluginutils/1.0.0-beta.19:
    '@rolldown/pluginutils': private
  /@rollup/pluginutils/5.2.0:
    '@rollup/pluginutils': private
  /@rollup/rollup-android-arm-eabi/4.44.1:
    '@rollup/rollup-android-arm-eabi': private
  /@rollup/rollup-android-arm64/4.44.1:
    '@rollup/rollup-android-arm64': private
  /@rollup/rollup-darwin-arm64/4.44.1:
    '@rollup/rollup-darwin-arm64': private
  /@rollup/rollup-darwin-x64/4.44.1:
    '@rollup/rollup-darwin-x64': private
  /@rollup/rollup-freebsd-arm64/4.44.1:
    '@rollup/rollup-freebsd-arm64': private
  /@rollup/rollup-freebsd-x64/4.44.1:
    '@rollup/rollup-freebsd-x64': private
  /@rollup/rollup-linux-arm-gnueabihf/4.44.1:
    '@rollup/rollup-linux-arm-gnueabihf': private
  /@rollup/rollup-linux-arm-musleabihf/4.44.1:
    '@rollup/rollup-linux-arm-musleabihf': private
  /@rollup/rollup-linux-arm64-gnu/4.44.1:
    '@rollup/rollup-linux-arm64-gnu': private
  /@rollup/rollup-linux-arm64-musl/4.44.1:
    '@rollup/rollup-linux-arm64-musl': private
  /@rollup/rollup-linux-loongarch64-gnu/4.44.1:
    '@rollup/rollup-linux-loongarch64-gnu': private
  /@rollup/rollup-linux-powerpc64le-gnu/4.44.1:
    '@rollup/rollup-linux-powerpc64le-gnu': private
  /@rollup/rollup-linux-riscv64-gnu/4.44.1:
    '@rollup/rollup-linux-riscv64-gnu': private
  /@rollup/rollup-linux-riscv64-musl/4.44.1:
    '@rollup/rollup-linux-riscv64-musl': private
  /@rollup/rollup-linux-s390x-gnu/4.44.1:
    '@rollup/rollup-linux-s390x-gnu': private
  /@rollup/rollup-linux-x64-gnu/4.44.1:
    '@rollup/rollup-linux-x64-gnu': private
  /@rollup/rollup-linux-x64-musl/4.44.1:
    '@rollup/rollup-linux-x64-musl': private
  /@rollup/rollup-win32-arm64-msvc/4.44.1:
    '@rollup/rollup-win32-arm64-msvc': private
  /@rollup/rollup-win32-ia32-msvc/4.44.1:
    '@rollup/rollup-win32-ia32-msvc': private
  /@rollup/rollup-win32-x64-msvc/4.44.1:
    '@rollup/rollup-win32-x64-msvc': private
  /@rushstack/node-core-library/4.0.2(@types/node@22.16.0):
    '@rushstack/node-core-library': private
  /@rushstack/rig-package/0.5.2:
    '@rushstack/rig-package': private
  /@rushstack/terminal/0.10.0(@types/node@22.16.0):
    '@rushstack/terminal': private
  /@rushstack/ts-command-line/4.19.1(@types/node@22.16.0):
    '@rushstack/ts-command-line': private
  /@sec-ant/readable-stream/0.4.1:
    '@sec-ant/readable-stream': private
  /@sindresorhus/merge-streams/4.0.0:
    '@sindresorhus/merge-streams': private
  /@storybook/addon-actions/8.6.14(storybook@8.6.14):
    '@storybook/addon-actions': private
  /@storybook/addon-backgrounds/8.6.14(storybook@8.6.14):
    '@storybook/addon-backgrounds': private
  /@storybook/addon-controls/8.6.14(storybook@8.6.14):
    '@storybook/addon-controls': private
  /@storybook/addon-docs/8.6.14(@types/react@19.1.8)(storybook@8.6.14):
    '@storybook/addon-docs': private
  /@storybook/addon-essentials/8.6.14(@types/react@19.1.8)(storybook@8.6.14):
    '@storybook/addon-essentials': private
  /@storybook/addon-highlight/8.6.14(storybook@8.6.14):
    '@storybook/addon-highlight': private
  /@storybook/addon-interactions/8.6.14(storybook@8.6.14):
    '@storybook/addon-interactions': private
  /@storybook/addon-links/8.6.14(react@19.1.0)(storybook@8.6.14):
    '@storybook/addon-links': private
  /@storybook/addon-measure/8.6.14(storybook@8.6.14):
    '@storybook/addon-measure': private
  /@storybook/addon-outline/8.6.14(storybook@8.6.14):
    '@storybook/addon-outline': private
  /@storybook/addon-toolbars/8.6.14(storybook@8.6.14):
    '@storybook/addon-toolbars': private
  /@storybook/addon-viewport/8.6.14(storybook@8.6.14):
    '@storybook/addon-viewport': private
  /@storybook/blocks/8.6.14(react-dom@19.1.0)(react@19.1.0)(storybook@8.6.14):
    '@storybook/blocks': private
  /@storybook/builder-vite/8.6.14(storybook@8.6.14)(vite@7.0.0):
    '@storybook/builder-vite': private
  /@storybook/components/8.6.14(storybook@8.6.14):
    '@storybook/components': private
  /@storybook/core/8.6.14(storybook@8.6.14):
    '@storybook/core': private
  /@storybook/csf-plugin/8.6.14(storybook@8.6.14):
    '@storybook/csf-plugin': private
  /@storybook/global/5.0.0:
    '@storybook/global': private
  /@storybook/icons/1.4.0(react-dom@19.1.0)(react@19.1.0):
    '@storybook/icons': private
  /@storybook/instrumenter/8.6.14(storybook@8.6.14):
    '@storybook/instrumenter': private
  /@storybook/manager-api/8.6.14(storybook@8.6.14):
    '@storybook/manager-api': private
  /@storybook/preview-api/8.6.14(storybook@8.6.14):
    '@storybook/preview-api': private
  /@storybook/react-dom-shim/8.6.14(react-dom@19.1.0)(react@19.1.0)(storybook@8.6.14):
    '@storybook/react-dom-shim': private
  /@storybook/test/8.6.14(storybook@8.6.14):
    '@storybook/test': private
  /@storybook/theming/8.6.14(storybook@8.6.14):
    '@storybook/theming': private
  /@storybook/vue3-vite/8.6.14(storybook@8.6.14)(vite@7.0.0)(vue@3.5.17):
    '@storybook/vue3-vite': private
  /@storybook/vue3/8.6.14(storybook@8.6.14)(vue@3.5.17):
    '@storybook/vue3': private
  /@testing-library/dom/10.4.0:
    '@testing-library/dom': private
  /@testing-library/jest-dom/6.5.0:
    '@testing-library/jest-dom': private
  /@testing-library/user-event/14.5.2(@testing-library/dom@10.4.0):
    '@testing-library/user-event': private
  /@tsconfig/node22/22.0.2:
    '@tsconfig/node22': private
  /@types/argparse/1.0.38:
    '@types/argparse': private
  /@types/aria-query/5.0.4:
    '@types/aria-query': private
  /@types/chai/5.2.2:
    '@types/chai': private
  /@types/deep-eql/4.0.2:
    '@types/deep-eql': private
  /@types/estree/1.0.8:
    '@types/estree': private
  /@types/jsdom/21.1.7:
    '@types/jsdom': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/mdx/2.0.13:
    '@types/mdx': private
  /@types/react/19.1.8:
    '@types/react': private
  /@types/tough-cookie/4.0.5:
    '@types/tough-cookie': private
  /@types/uuid/9.0.8:
    '@types/uuid': private
  /@typescript-eslint/eslint-plugin/8.35.1(@typescript-eslint/parser@8.35.1)(eslint@9.30.1)(typescript@5.8.3):
    '@typescript-eslint/eslint-plugin': public
  /@typescript-eslint/parser/8.35.1(eslint@9.30.1)(typescript@5.8.3):
    '@typescript-eslint/parser': public
  /@typescript-eslint/project-service/8.35.1(typescript@5.8.3):
    '@typescript-eslint/project-service': public
  /@typescript-eslint/scope-manager/8.35.1:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/tsconfig-utils/8.35.1(typescript@5.8.3):
    '@typescript-eslint/tsconfig-utils': public
  /@typescript-eslint/type-utils/8.35.1(eslint@9.30.1)(typescript@5.8.3):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/8.35.1:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/8.35.1(typescript@5.8.3):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/8.35.1(eslint@9.30.1)(typescript@5.8.3):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/8.35.1:
    '@typescript-eslint/visitor-keys': public
  /@vitejs/plugin-vue/6.0.0(vite@7.0.0)(vue@3.5.17):
    '@vitejs/plugin-vue': private
  /@vitest/eslint-plugin/1.3.4(eslint@9.30.1)(typescript@5.8.3)(vitest@3.2.4):
    '@vitest/eslint-plugin': public
  /@vitest/expect/2.0.5:
    '@vitest/expect': private
  /@vitest/mocker/3.2.4(vite@7.0.0):
    '@vitest/mocker': private
  /@vitest/pretty-format/3.2.4:
    '@vitest/pretty-format': private
  /@vitest/runner/3.2.4:
    '@vitest/runner': private
  /@vitest/snapshot/3.2.4:
    '@vitest/snapshot': private
  /@vitest/spy/2.0.5:
    '@vitest/spy': private
  /@vitest/utils/3.2.4:
    '@vitest/utils': private
  /@volar/language-core/2.4.15:
    '@volar/language-core': private
  /@volar/source-map/1.11.1:
    '@volar/source-map': private
  /@volar/typescript/2.4.15:
    '@volar/typescript': private
  /@vue/babel-helper-vue-transform-on/1.4.0:
    '@vue/babel-helper-vue-transform-on': private
  /@vue/babel-plugin-jsx/1.4.0(@babel/core@7.28.0):
    '@vue/babel-plugin-jsx': private
  /@vue/babel-plugin-resolve-type/1.4.0(@babel/core@7.28.0):
    '@vue/babel-plugin-resolve-type': private
  /@vue/compiler-core/3.5.17:
    '@vue/compiler-core': private
  /@vue/compiler-dom/3.5.17:
    '@vue/compiler-dom': private
  /@vue/compiler-sfc/3.5.17:
    '@vue/compiler-sfc': private
  /@vue/compiler-ssr/3.5.17:
    '@vue/compiler-ssr': private
  /@vue/compiler-vue2/2.7.16:
    '@vue/compiler-vue2': private
  /@vue/devtools-core/7.7.7(vite@7.0.0)(vue@3.5.17):
    '@vue/devtools-core': private
  /@vue/devtools-kit/7.7.7:
    '@vue/devtools-kit': private
  /@vue/devtools-shared/7.7.7:
    '@vue/devtools-shared': private
  /@vue/eslint-config-prettier/10.2.0(eslint@9.30.1)(prettier@3.5.3):
    '@vue/eslint-config-prettier': public
  /@vue/eslint-config-typescript/14.6.0(eslint-plugin-vue@10.2.0)(eslint@9.30.1)(typescript@5.8.3):
    '@vue/eslint-config-typescript': public
  /@vue/language-core/1.8.27(typescript@5.8.3):
    '@vue/language-core': private
  /@vue/reactivity/3.5.17:
    '@vue/reactivity': private
  /@vue/runtime-core/3.5.17:
    '@vue/runtime-core': private
  /@vue/runtime-dom/3.5.17:
    '@vue/runtime-dom': private
  /@vue/server-renderer/3.5.17(vue@3.5.17):
    '@vue/server-renderer': private
  /@vue/shared/3.5.17:
    '@vue/shared': private
  /@vue/test-utils/2.4.6:
    '@vue/test-utils': private
  /@vue/tsconfig/0.7.0(typescript@5.8.3)(vue@3.5.17):
    '@vue/tsconfig': private
  /abbrev/2.0.0:
    abbrev: private
  /acorn-jsx/5.3.2(acorn@8.15.0):
    acorn-jsx: private
  /acorn/8.15.0:
    acorn: private
  /agent-base/7.1.3:
    agent-base: private
  /ajv/6.12.6:
    ajv: private
  /alien-signals/1.0.13:
    alien-signals: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/6.2.1:
    ansi-styles: private
  /any-promise/1.3.0:
    any-promise: private
  /anymatch/3.1.3:
    anymatch: private
  /arg/5.0.2:
    arg: private
  /argparse/1.0.10:
    argparse: private
  /aria-query/5.3.0:
    aria-query: private
  /asap/2.0.6:
    asap: private
  /assert-never/1.4.0:
    assert-never: private
  /assertion-error/2.0.1:
    assertion-error: private
  /ast-types/0.16.1:
    ast-types: private
  /autoprefixer/10.4.21(postcss@8.5.6):
    autoprefixer: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /babel-walk/3.0.0-canary-5:
    babel-walk: private
  /balanced-match/1.0.2:
    balanced-match: private
  /better-opn/3.0.2:
    better-opn: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /birpc/2.4.0:
    birpc: private
  /boolbase/1.0.0:
    boolbase: private
  /brace-expansion/1.1.12:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browser-assert/1.2.1:
    browser-assert: private
  /browserslist/4.25.1:
    browserslist: private
  /bundle-name/4.1.0:
    bundle-name: private
  /cac/6.7.14:
    cac: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.8:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /camelcase-css/2.0.1:
    camelcase-css: private
  /caniuse-lite/1.0.30001726:
    caniuse-lite: private
  /chai/5.2.0:
    chai: private
  /chalk/4.1.2:
    chalk: private
  /character-parser/2.2.0:
    character-parser: private
  /check-error/2.1.1:
    check-error: private
  /chokidar/3.6.0:
    chokidar: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /commander/4.1.1:
    commander: private
  /computeds/0.0.1:
    computeds: private
  /concat-map/0.0.1:
    concat-map: private
  /config-chain/1.1.13:
    config-chain: private
  /constantinople/4.0.1:
    constantinople: private
  /convert-source-map/2.0.0:
    convert-source-map: private
  /copy-anything/3.0.5:
    copy-anything: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /css.escape/1.5.1:
    css.escape: private
  /cssesc/3.0.0:
    cssesc: private
  /cssstyle/4.6.0:
    cssstyle: private
  /csstype/3.1.3:
    csstype: private
  /data-urls/5.0.0:
    data-urls: private
  /de-indent/1.0.2:
    de-indent: private
  /debug/4.4.1:
    debug: private
  /decimal.js/10.5.0:
    decimal.js: private
  /deep-eql/5.0.2:
    deep-eql: private
  /deep-is/0.1.4:
    deep-is: private
  /default-browser-id/5.0.0:
    default-browser-id: private
  /default-browser/5.2.1:
    default-browser: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-lazy-prop/3.0.0:
    define-lazy-prop: private
  /dequal/2.0.3:
    dequal: private
  /didyoumean/1.2.2:
    didyoumean: private
  /dlv/1.1.3:
    dlv: private
  /doctypes/1.1.0:
    doctypes: private
  /dom-accessibility-api/0.5.16:
    dom-accessibility-api: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /editorconfig/1.0.4:
    editorconfig: private
  /electron-to-chromium/1.5.179:
    electron-to-chromium: private
  /emoji-regex/8.0.0:
    emoji-regex: private
  /entities/4.5.0:
    entities: private
  /error-stack-parser-es/0.1.5:
    error-stack-parser-es: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-module-lexer/1.7.0:
    es-module-lexer: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /esbuild-register/3.6.0(esbuild@0.25.5):
    esbuild-register: private
  /esbuild/0.25.5:
    esbuild: private
  /escalade/3.2.0:
    escalade: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /eslint-config-prettier/10.1.5(eslint@9.30.1):
    eslint-config-prettier: public
  /eslint-plugin-playwright/2.2.0(eslint@9.30.1):
    eslint-plugin-playwright: public
  /eslint-plugin-prettier/5.5.1(eslint-config-prettier@10.1.5)(eslint@9.30.1)(prettier@3.5.3):
    eslint-plugin-prettier: public
  /eslint-plugin-vue/10.2.0(eslint@9.30.1)(vue-eslint-parser@10.2.0):
    eslint-plugin-vue: public
  /eslint-scope/8.4.0:
    eslint-scope: public
  /eslint-visitor-keys/4.2.1:
    eslint-visitor-keys: public
  /eslint/9.30.1(jiti@2.4.2):
    eslint: public
  /esm-resolve/1.0.11:
    esm-resolve: private
  /espree/10.4.0:
    espree: private
  /esprima/4.0.1:
    esprima: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /estree-walker/2.0.2:
    estree-walker: private
  /esutils/2.0.3:
    esutils: private
  /execa/9.6.0:
    execa: private
  /expect-type/1.2.1:
    expect-type: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-diff/1.3.0:
    fast-diff: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fastq/1.19.1:
    fastq: private
  /fdir/6.4.6(picomatch@4.0.2):
    fdir: private
  /figures/6.1.0:
    figures: private
  /file-entry-cache/8.0.0:
    file-entry-cache: private
  /fill-range/7.1.1:
    fill-range: private
  /find-package-json/1.2.0:
    find-package-json: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/4.0.1:
    flat-cache: private
  /flatted/3.3.3:
    flatted: private
  /for-each/0.3.5:
    for-each: private
  /foreground-child/3.3.1:
    foreground-child: private
  /fraction.js/4.3.7:
    fraction.js: private
  /fs-extra/11.3.0:
    fs-extra: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /gensync/1.0.0-beta.2:
    gensync: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stream/9.0.1:
    get-stream: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob/10.4.5:
    glob: private
  /globals/13.24.0:
    globals: private
  /gopd/1.2.0:
    gopd: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /graphemer/1.4.0:
    graphemer: private
  /has-flag/4.0.0:
    has-flag: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hash-sum/2.0.0:
    hash-sum: private
  /hasown/2.0.2:
    hasown: private
  /he/1.2.0:
    he: private
  /hookable/5.5.3:
    hookable: private
  /html-encoding-sniffer/4.0.0:
    html-encoding-sniffer: private
  /http-proxy-agent/7.0.2:
    http-proxy-agent: private
  /https-proxy-agent/7.0.6:
    https-proxy-agent: private
  /human-signals/8.0.1:
    human-signals: private
  /iconv-lite/0.6.3:
    iconv-lite: private
  /ignore/5.3.2:
    ignore: private
  /import-fresh/3.3.1:
    import-fresh: private
  /import-lazy/4.0.0:
    import-lazy: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /indent-string/4.0.0:
    indent-string: private
  /inherits/2.0.4:
    inherits: private
  /ini/1.3.8:
    ini: private
  /is-arguments/1.2.0:
    is-arguments: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-callable/1.2.7:
    is-callable: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-docker/3.0.0:
    is-docker: private
  /is-expression/4.0.0:
    is-expression: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-generator-function/1.1.0:
    is-generator-function: private
  /is-glob/4.0.3:
    is-glob: private
  /is-inside-container/1.0.0:
    is-inside-container: private
  /is-number/7.0.0:
    is-number: private
  /is-plain-obj/4.1.0:
    is-plain-obj: private
  /is-potential-custom-element-name/1.0.1:
    is-potential-custom-element-name: private
  /is-promise/2.2.2:
    is-promise: private
  /is-regex/1.2.1:
    is-regex: private
  /is-stream/4.0.1:
    is-stream: private
  /is-typed-array/1.1.15:
    is-typed-array: private
  /is-unicode-supported/2.1.0:
    is-unicode-supported: private
  /is-what/4.1.16:
    is-what: private
  /is-wsl/3.1.0:
    is-wsl: private
  /isexe/3.1.1:
    isexe: private
  /jackspeak/3.4.3:
    jackspeak: private
  /jiti/2.4.2:
    jiti: private
  /jju/1.4.0:
    jju: private
  /js-beautify/1.15.4:
    js-beautify: private
  /js-cookie/3.0.5:
    js-cookie: private
  /js-stringify/1.0.2:
    js-stringify: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /jsdoc-type-pratt-parser/4.1.0:
    jsdoc-type-pratt-parser: private
  /jsdom/26.1.0:
    jsdom: private
  /jsesc/3.1.0:
    jsesc: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-parse-even-better-errors/4.0.0:
    json-parse-even-better-errors: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json5/2.2.3:
    json5: private
  /jsonfile/6.1.0:
    jsonfile: private
  /jstransformer/1.0.0:
    jstransformer: private
  /keyv/4.5.4:
    keyv: private
  /kolorist/1.8.0:
    kolorist: private
  /levn/0.4.1:
    levn: private
  /lilconfig/3.1.3:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash.get/4.4.2:
    lodash.get: private
  /lodash.isequal/4.5.0:
    lodash.isequal: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /lodash/4.17.21:
    lodash: private
  /loupe/3.1.4:
    loupe: private
  /lru-cache/8.0.5:
    lru-cache: private
  /lz-string/1.5.0:
    lz-string: private
  /magic-string/0.30.17:
    magic-string: private
  /map-or-similar/1.5.0:
    map-or-similar: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /memoizerific/1.11.3:
    memoizerific: private
  /memorystream/0.3.1:
    memorystream: private
  /merge2/1.4.1:
    merge2: private
  /micromatch/4.0.8:
    micromatch: private
  /min-indent/1.0.1:
    min-indent: private
  /minimatch/3.1.2:
    minimatch: private
  /minipass/7.1.2:
    minipass: private
  /mitt/3.0.1:
    mitt: private
  /mrmime/2.0.1:
    mrmime: private
  /ms/2.1.3:
    ms: private
  /muggle-string/0.3.1:
    muggle-string: private
  /mz/2.7.0:
    mz: private
  /nanoid/3.3.11:
    nanoid: private
  /natural-compare/1.4.0:
    natural-compare: private
  /node-releases/2.0.19:
    node-releases: private
  /nopt/7.2.1:
    nopt: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-range/0.1.2:
    normalize-range: private
  /npm-normalize-package-bin/4.0.0:
    npm-normalize-package-bin: private
  /npm-run-all2/8.0.4:
    npm-run-all2: private
  /npm-run-path/6.0.0:
    npm-run-path: private
  /nth-check/2.1.1:
    nth-check: private
  /nwsapi/2.2.20:
    nwsapi: private
  /object-assign/4.1.1:
    object-assign: private
  /object-hash/3.0.0:
    object-hash: private
  /open/10.1.2:
    open: private
  /optionator/0.9.4:
    optionator: private
  /p-limit/3.1.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /parent-module/1.0.1:
    parent-module: private
  /parse-ms/4.0.0:
    parse-ms: private
  /parse5/7.3.0:
    parse5: private
  /path-browserify/1.0.1:
    path-browserify: private
  /path-exists/4.0.0:
    path-exists: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /pathe/2.0.3:
    pathe: private
  /pathval/2.0.1:
    pathval: private
  /perfect-debounce/1.0.0:
    perfect-debounce: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/4.0.2:
    picomatch: private
  /pidtree/0.6.0:
    pidtree: private
  /pify/2.3.0:
    pify: private
  /pirates/4.0.7:
    pirates: private
  /playwright-core/1.53.2:
    playwright-core: private
  /playwright/1.53.2:
    playwright: private
  /polished/4.3.1:
    polished: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /postcss-import/15.1.0(postcss@8.5.6):
    postcss-import: private
  /postcss-js/4.0.1(postcss@8.5.6):
    postcss-js: private
  /postcss-load-config/4.0.2(postcss@8.5.6):
    postcss-load-config: private
  /postcss-nested/6.2.0(postcss@8.5.6):
    postcss-nested: private
  /postcss-selector-parser/6.1.2:
    postcss-selector-parser: private
  /postcss-value-parser/4.2.0:
    postcss-value-parser: private
  /postcss/8.5.6:
    postcss: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /prettier-linter-helpers/1.0.0:
    prettier-linter-helpers: public
  /prettier/3.5.3:
    prettier: public
  /pretty-format/27.5.1:
    pretty-format: private
  /pretty-ms/9.2.0:
    pretty-ms: private
  /process/0.11.10:
    process: private
  /promise/7.3.1:
    promise: private
  /proto-list/1.2.4:
    proto-list: private
  /pug-attrs/3.0.0:
    pug-attrs: private
  /pug-code-gen/3.0.3:
    pug-code-gen: private
  /pug-error/2.1.0:
    pug-error: private
  /pug-filters/4.0.0:
    pug-filters: private
  /pug-lexer/5.0.1:
    pug-lexer: private
  /pug-linker/4.0.0:
    pug-linker: private
  /pug-load/3.0.0:
    pug-load: private
  /pug-parser/6.0.0:
    pug-parser: private
  /pug-runtime/3.0.1:
    pug-runtime: private
  /pug-strip-comments/2.0.0:
    pug-strip-comments: private
  /pug-walk/2.0.0:
    pug-walk: private
  /pug/3.0.3:
    pug: private
  /punycode/2.3.1:
    punycode: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /react-dom/19.1.0(react@19.1.0):
    react-dom: private
  /react-is/17.0.2:
    react-is: private
  /react/19.1.0:
    react: private
  /read-cache/1.0.0:
    read-cache: private
  /read-package-json-fast/4.0.0:
    read-package-json-fast: private
  /readdirp/3.6.0:
    readdirp: private
  /recast/0.23.11:
    recast: private
  /redent/3.0.0:
    redent: private
  /resolve-from/4.0.0:
    resolve-from: private
  /resolve/1.22.10:
    resolve: private
  /reusify/1.1.0:
    reusify: private
  /rfdc/1.4.1:
    rfdc: private
  /rimraf/5.0.10:
    rimraf: private
  /rollup/4.44.1:
    rollup: private
  /rrweb-cssom/0.8.0:
    rrweb-cssom: private
  /run-applescript/7.0.0:
    run-applescript: private
  /run-parallel/1.2.0:
    run-parallel: private
  /safe-regex-test/1.1.0:
    safe-regex-test: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /saxes/6.0.0:
    saxes: private
  /scheduler/0.26.0:
    scheduler: private
  /semver/7.7.2:
    semver: private
  /set-function-length/1.2.2:
    set-function-length: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /shell-quote/1.8.3:
    shell-quote: private
  /siginfo/2.0.0:
    siginfo: private
  /signal-exit/4.1.0:
    signal-exit: private
  /sirv/3.0.1:
    sirv: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map/0.6.1:
    source-map: private
  /speakingurl/14.0.1:
    speakingurl: private
  /sprintf-js/1.0.3:
    sprintf-js: private
  /stackback/0.0.2:
    stackback: private
  /std-env/3.9.0:
    std-env: private
  /storybook/8.6.14:
    storybook: private
  /string-argv/0.3.2:
    string-argv: private
  /string-width/4.2.3:
    string-width-cjs: private
  /string-width/5.1.2:
    string-width: private
  /strip-ansi/6.0.1:
    strip-ansi-cjs: private
  /strip-ansi/7.1.0:
    strip-ansi: private
  /strip-final-newline/4.0.0:
    strip-final-newline: private
  /strip-indent/3.0.0:
    strip-indent: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /strip-literal/3.0.0:
    strip-literal: private
  /sucrase/3.35.0:
    sucrase: private
  /superjson/2.2.2:
    superjson: private
  /supports-color/7.2.0:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /symbol-tree/3.2.4:
    symbol-tree: private
  /synckit/0.11.8:
    synckit: private
  /tailwindcss/3.4.17:
    tailwindcss: private
  /thenify-all/1.6.0:
    thenify-all: private
  /thenify/3.3.1:
    thenify: private
  /tiny-invariant/1.3.3:
    tiny-invariant: private
  /tinybench/2.9.0:
    tinybench: private
  /tinyexec/0.3.2:
    tinyexec: private
  /tinyglobby/0.2.14:
    tinyglobby: private
  /tinypool/1.1.1:
    tinypool: private
  /tinyrainbow/2.0.0:
    tinyrainbow: private
  /tinyspy/3.0.2:
    tinyspy: private
  /tldts-core/6.1.86:
    tldts-core: private
  /tldts/6.1.86:
    tldts: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /token-stream/1.0.0:
    token-stream: private
  /totalist/3.0.1:
    totalist: private
  /tough-cookie/5.1.2:
    tough-cookie: private
  /tr46/5.1.1:
    tr46: private
  /ts-api-utils/2.1.0(typescript@5.8.3):
    ts-api-utils: private
  /ts-dedent/2.2.0:
    ts-dedent: private
  /ts-interface-checker/0.1.13:
    ts-interface-checker: private
  /ts-map/1.0.3:
    ts-map: private
  /tslib/2.8.1:
    tslib: private
  /type-check/0.4.0:
    type-check: private
  /type-fest/2.19.0:
    type-fest: private
  /typescript-eslint/8.35.1(eslint@9.30.1)(typescript@5.8.3):
    typescript-eslint: public
  /undici-types/6.21.0:
    undici-types: private
  /unicorn-magic/0.3.0:
    unicorn-magic: private
  /universalify/2.0.1:
    universalify: private
  /unplugin/1.16.1:
    unplugin: private
  /update-browserslist-db/1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  /uri-js/4.4.1:
    uri-js: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /util/0.12.5:
    util: private
  /uuid/9.0.1:
    uuid: private
  /validator/13.15.15:
    validator: private
  /vite-hot-client/2.1.0(vite@7.0.0):
    vite-hot-client: private
  /vite-node/3.2.4(@types/node@22.16.0)(jiti@2.4.2):
    vite-node: private
  /vite-plugin-dts/3.9.1(@types/node@22.16.0)(typescript@5.8.3)(vite@7.0.0):
    vite-plugin-dts: private
  /vite-plugin-inspect/0.8.9(vite@7.0.0):
    vite-plugin-inspect: private
  /vite-plugin-vue-devtools/7.7.7(vite@7.0.0)(vue@3.5.17):
    vite-plugin-vue-devtools: private
  /vite-plugin-vue-inspector/5.3.2(vite@7.0.0):
    vite-plugin-vue-inspector: private
  /vite/7.0.0(@types/node@22.16.0)(jiti@2.4.2):
    vite: private
  /vitest/3.2.4(@types/node@22.16.0)(jiti@2.4.2)(jsdom@26.1.0):
    vitest: private
  /void-elements/3.1.0:
    void-elements: private
  /vscode-uri/3.1.0:
    vscode-uri: private
  /vue-component-meta/2.2.12(typescript@5.8.3):
    vue-component-meta: private
  /vue-component-type-helpers/3.0.1:
    vue-component-type-helpers: private
  /vue-docgen-api/4.79.2(vue@3.5.17):
    vue-docgen-api: private
  /vue-eslint-parser/10.2.0(eslint@9.30.1):
    vue-eslint-parser: public
  /vue-inbrowser-compiler-independent-utils/4.71.1(vue@3.5.17):
    vue-inbrowser-compiler-independent-utils: private
  /vue-template-compiler/2.7.16:
    vue-template-compiler: private
  /vue-tsc/2.2.12(typescript@5.8.3):
    vue-tsc: private
  /vue/3.5.17(typescript@5.8.3):
    vue: private
  /w3c-xmlserializer/5.0.0:
    w3c-xmlserializer: private
  /webidl-conversions/7.0.0:
    webidl-conversions: private
  /webpack-virtual-modules/0.6.2:
    webpack-virtual-modules: private
  /whatwg-encoding/3.1.1:
    whatwg-encoding: private
  /whatwg-mimetype/4.0.0:
    whatwg-mimetype: private
  /whatwg-url/14.2.0:
    whatwg-url: private
  /which-typed-array/1.1.19:
    which-typed-array: private
  /which/5.0.0:
    which: private
  /why-is-node-running/2.3.0:
    why-is-node-running: private
  /with/7.0.2:
    with: private
  /word-wrap/1.2.5:
    word-wrap: private
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrap-ansi/8.1.0:
    wrap-ansi: private
  /ws/8.18.3:
    ws: private
  /xml-name-validator/4.0.0:
    xml-name-validator: private
  /xmlchars/2.2.0:
    xmlchars: private
  /yallist/4.0.0:
    yallist: private
  /yaml/2.8.0:
    yaml: private
  /yocto-queue/0.1.0:
    yocto-queue: private
  /yoctocolors/2.1.1:
    yoctocolors: private
  /z-schema/5.0.5:
    z-schema: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.0
pendingBuilds: []
prunedAt: Thu, 03 Jul 2025 01:25:25 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@esbuild/aix-ppc64/0.25.5
  - /@esbuild/android-arm/0.25.5
  - /@esbuild/android-arm64/0.25.5
  - /@esbuild/android-x64/0.25.5
  - /@esbuild/darwin-arm64/0.25.5
  - /@esbuild/darwin-x64/0.25.5
  - /@esbuild/freebsd-arm64/0.25.5
  - /@esbuild/freebsd-x64/0.25.5
  - /@esbuild/linux-arm/0.25.5
  - /@esbuild/linux-arm64/0.25.5
  - /@esbuild/linux-ia32/0.25.5
  - /@esbuild/linux-loong64/0.25.5
  - /@esbuild/linux-mips64el/0.25.5
  - /@esbuild/linux-ppc64/0.25.5
  - /@esbuild/linux-riscv64/0.25.5
  - /@esbuild/linux-s390x/0.25.5
  - /@esbuild/linux-x64/0.25.5
  - /@esbuild/netbsd-arm64/0.25.5
  - /@esbuild/netbsd-x64/0.25.5
  - /@esbuild/openbsd-arm64/0.25.5
  - /@esbuild/openbsd-x64/0.25.5
  - /@esbuild/sunos-x64/0.25.5
  - /@esbuild/win32-arm64/0.25.5
  - /@esbuild/win32-ia32/0.25.5
  - /@rollup/rollup-android-arm-eabi/4.44.1
  - /@rollup/rollup-android-arm64/4.44.1
  - /@rollup/rollup-darwin-arm64/4.44.1
  - /@rollup/rollup-darwin-x64/4.44.1
  - /@rollup/rollup-freebsd-arm64/4.44.1
  - /@rollup/rollup-freebsd-x64/4.44.1
  - /@rollup/rollup-linux-arm-gnueabihf/4.44.1
  - /@rollup/rollup-linux-arm-musleabihf/4.44.1
  - /@rollup/rollup-linux-arm64-gnu/4.44.1
  - /@rollup/rollup-linux-arm64-musl/4.44.1
  - /@rollup/rollup-linux-loongarch64-gnu/4.44.1
  - /@rollup/rollup-linux-powerpc64le-gnu/4.44.1
  - /@rollup/rollup-linux-riscv64-gnu/4.44.1
  - /@rollup/rollup-linux-riscv64-musl/4.44.1
  - /@rollup/rollup-linux-s390x-gnu/4.44.1
  - /@rollup/rollup-linux-x64-gnu/4.44.1
  - /@rollup/rollup-linux-x64-musl/4.44.1
  - /@rollup/rollup-win32-arm64-msvc/4.44.1
  - /@rollup/rollup-win32-ia32-msvc/4.44.1
  - /fsevents/2.3.2
  - /fsevents/2.3.3
storeDir: E:\.pnpm-store\v3
virtualStoreDir: E:\code\work\components\node_modules\.pnpm
