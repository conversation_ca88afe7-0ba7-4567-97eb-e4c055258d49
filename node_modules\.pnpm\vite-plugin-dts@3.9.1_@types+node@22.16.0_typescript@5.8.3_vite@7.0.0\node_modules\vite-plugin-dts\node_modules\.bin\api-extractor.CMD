@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\@microsoft+api-extractor@7.43.0_@types+node@22.16.0\node_modules\@microsoft\api-extractor\bin\node_modules;E:\code\work\components\node_modules\.pnpm\@microsoft+api-extractor@7.43.0_@types+node@22.16.0\node_modules\@microsoft\api-extractor\node_modules;E:\code\work\components\node_modules\.pnpm\@microsoft+api-extractor@7.43.0_@types+node@22.16.0\node_modules\@microsoft\node_modules;E:\code\work\components\node_modules\.pnpm\@microsoft+api-extractor@7.43.0_@types+node@22.16.0\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\code\work\components\node_modules\.pnpm\@microsoft+api-extractor@7.43.0_@types+node@22.16.0\node_modules\@microsoft\api-extractor\bin\node_modules;E:\code\work\components\node_modules\.pnpm\@microsoft+api-extractor@7.43.0_@types+node@22.16.0\node_modules\@microsoft\api-extractor\node_modules;E:\code\work\components\node_modules\.pnpm\@microsoft+api-extractor@7.43.0_@types+node@22.16.0\node_modules\@microsoft\node_modules;E:\code\work\components\node_modules\.pnpm\@microsoft+api-extractor@7.43.0_@types+node@22.16.0\node_modules;E:\code\work\components\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\@microsoft+api-extractor@7.43.0_@types+node@22.16.0\node_modules\@microsoft\api-extractor\bin\api-extractor" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\@microsoft+api-extractor@7.43.0_@types+node@22.16.0\node_modules\@microsoft\api-extractor\bin\api-extractor" %*
)
