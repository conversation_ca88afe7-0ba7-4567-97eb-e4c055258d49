{"version": 3, "file": "fork.js", "sourceRoot": "", "sources": ["../src/fork.ts"], "names": [], "mappings": ";;;AAAA,0DAAkC;AAClC,wEAA+C;AAC/C,0DAAkC;AAClC,wDAAgC;AAChC,kEAAyC;AAEzC,mCAAiD;AAEjD,mBAAyB,OAAsB;IAC7C,IAAM,IAAI,GAAG,UAAU,EAAE,CAAC;IAE1B,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,eAAW,CAAC,CAAC;IAEpC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE1B,KAAK,CAAC,QAAQ,EAAE,CAAC;IAEjB,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,sBAAiB,CAAC,CAAC;IAEhD,OAAO;QACL,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,YAAY,EAAE,KAAK,CAAC,YAAY;QAChC,UAAU,EAAE,KAAK,CAAC,UAAU;QAC5B,QAAQ,EAAE,KAAK,CAAC,QAAQ;QACxB,YAAY,EAAE,KAAK,CAAC,YAAY;QAChC,aAAa,EAAE,KAAK,CAAC,aAAa;QAClC,aAAa,EAAE,KAAK,CAAC,aAAa;QAClC,SAAS,EAAE,KAAK,CAAC,SAAS;QAC1B,SAAS,EAAE,KAAK,CAAC,SAAS;QAC1B,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;QAC1C,cAAc,EAAE,KAAK,CAAC,cAAc;QACpC,qBAAqB,EAAE,IAAI,CAAC,GAAG,CAAC,eAAW,CAAC;QAC5C,QAAQ,EAAE,KAAK,CAAC,QAAQ;QACxB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,cAAU,CAAC;QAC1B,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,mBAAc,CAAC;QAClC,WAAW,aAAA;QACX,GAAG,EAAE,IAAI,CAAC,GAAG;QACb,KAAK,EAAE,WAAW,CAAC,KAAK;KACzB,CAAC;AACJ,CAAC;AA/BD,4BA+BC;AAAA,CAAC;AAEF,SAAS,UAAU;IACjB,IAAM,IAAI,GAAsB,EAAE,CAAC;IACnC,IAAM,UAAU,GAAc,EAAE,CAAC;IAEjC,SAAS,GAAG,CAAI,MAAiB;QAC/B,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;YACd,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAClB,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;SAChC;QACD,OAAO,UAAU,CAAC,GAAG,CAAM,CAAC;IAC9B,CAAC;IAED,IAAI,IAAI,GAAS,EAAE,GAAG,KAAA,EAAE,CAAC;IAEzB,OAAO,IAAI,CAAC;AACd,CAAC;AAED,IAAA,8BAAqB,EAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,CAAC"}