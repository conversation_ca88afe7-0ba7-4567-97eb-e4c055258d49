try{
(()=>{var y=__STORYBOOK_API__,{ActiveTabs:E,Consumer:S,ManagerContext:b,Provider:A,RequestResponseError:O,addons:r,combineParameters:T,controlOrMetaKey:h,controlOrMetaSymbol:v,eventMatchesShortcut:R,eventToShortcut:C,experimental_MockUniversalStore:U,experimental_UniversalStore:w,experimental_requestResponse:I,experimental_useUniversalStore:P,isMacLike:g,isShortcutTaken:x,keyToSymbol:D,merge:M,mockChannel:V,optionOrAltSymbol:L,shortcutMatchesShortcut:N,shortcutToHumanString:B,types:K,useAddonState:f,useArgTypes:q,useArgs:G,useChannel:Y,useGlobalTypes:$,useGlobals:H,useParameter:Q,useSharedState:j,useStoryPrepared:z,useStorybookApi:F,useStorybookState:J}=__STORYBOOK_API__;var o="storybook/links",d={NAVIGATE:`${o}/navigate`,REQUEST:`${o}/request`,RECEIVE:`${o}/receive`};r.register(o,e=>{e.on(d.REQUEST,({kind:m,name:l})=>{let a=e.storyId(m,l);e.emit(d.RECEIVE,a)})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
