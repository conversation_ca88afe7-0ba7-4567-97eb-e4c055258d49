import {
  b,
  cn,
  gt,
  nn,
  on,
  yn,
  z
} from "./chunk-Z5UZJ34K.js";
import "./chunk-TCB5WPCR.js";
import "./chunk-APLEHN7L.js";
import {
  isVNode,
  watch
} from "./chunk-Y4H6EBV6.js";
import {
  require_preview_api
} from "./chunk-RF4OLZDA.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// ../../node_modules/.pnpm/@storybook+vue3@8.6.14_storybook@8.6.14_vue@3.5.17/node_modules/@storybook/vue3/dist/entry-preview-docs.mjs
var import_preview_api = __toESM(require_preview_api(), 1);
var ARG_TYPE_SECTIONS = ["props", "events", "slots", "exposed", "expose"];
var extractArgTypes = (component) => {
  if (!z(component)) return null;
  let usedDocgenPlugin = "exposed" in component.__docgenInfo ? "vue-component-meta" : "vue-docgen-api", argTypes = {};
  return ARG_TYPE_SECTIONS.forEach((section) => {
    on(component, section).forEach((extractedProp) => {
      let argType;
      if (usedDocgenPlugin === "vue-docgen-api") {
        let docgenInfo = extractedProp.docgenInfo;
        argType = extractFromVueDocgenApi(docgenInfo, section, extractedProp);
      } else {
        let docgenInfo = extractedProp.docgenInfo;
        argType = extractFromVueComponentMeta(docgenInfo, section);
      }
      if (!argType || argTypes[argType.name]) return;
      ["events", "expose", "exposed"].includes(section) && (argType.control = { disable: true }), argTypes[argType.name] = argType;
    });
  }), argTypes;
};
var extractFromVueDocgenApi = (docgenInfo, section, extractedProp) => {
  let type, sbType;
  if (section === "events" && (type = docgenInfo.type?.names.join(), sbType = { name: "other", value: type ?? "", required: false }), section === "slots") {
    let slotBindings = docgenInfo.bindings?.filter((binding) => !!binding.name).map((binding) => `${binding.name}: ${binding.type?.name ?? "unknown"}`).join("; ");
    type = slotBindings ? `{ ${slotBindings} }` : void 0, sbType = { name: "other", value: type ?? "", required: false };
  }
  if (section === "props") {
    let propInfo = docgenInfo;
    if (type = propInfo.type?.name, sbType = extractedProp ? b(extractedProp.docgenInfo) : { name: "other", value: type }, propInfo.type && "elements" in propInfo.type && Array.isArray(propInfo.type.elements) && propInfo.type.elements.length > 0) {
      let elements = propInfo.type.elements.map((i) => i.name);
      type === "Array" && (type = `${elements.length === 1 ? elements[0] : `(${elements.join(" | ")})`}[]`), type === "union" ? type = elements.join(" | ") : type === "intersection" && (type = elements.join(" & "));
    }
  }
  let required = "required" in docgenInfo ? docgenInfo.required ?? false : false;
  return { name: docgenInfo.name, description: docgenInfo.description, type: sbType ? { ...sbType, required } : { name: "other", value: type ?? "" }, table: { type: type ? { summary: type } : void 0, defaultValue: extractedProp?.propDef.defaultValue ?? void 0, jsDocTags: extractedProp?.propDef.jsDocTags, category: section } };
};
var extractFromVueComponentMeta = (docgenInfo, section) => {
  if ("global" in docgenInfo && docgenInfo.global) return;
  let tableType = { summary: docgenInfo.type.replace(" | undefined", "") };
  if (section === "props") {
    let propInfo = docgenInfo, defaultValue = propInfo.default ? { summary: propInfo.default } : void 0;
    return { name: propInfo.name, description: formatDescriptionWithTags(propInfo.description, propInfo.tags), defaultValue, type: convertVueComponentMetaProp(propInfo), table: { type: tableType, defaultValue, category: section } };
  } else return { name: docgenInfo.name, description: "description" in docgenInfo ? docgenInfo.description : "", type: { name: "other", value: docgenInfo.type }, table: { type: tableType, category: section } };
};
var convertVueComponentMetaProp = (propInfo) => {
  let schema = propInfo.schema, required = propInfo.required, fallbackSbType = { name: "other", value: propInfo.type, required }, KNOWN_SCHEMAS = ["string", "number", "function", "boolean", "symbol"];
  if (typeof schema == "string") return KNOWN_SCHEMAS.includes(schema) ? { name: schema, required } : fallbackSbType;
  switch (schema.kind) {
    case "enum": {
      let definedSchemas = schema.schema?.filter((item) => item !== "undefined") ?? [];
      return isBooleanSchema(definedSchemas) ? { name: "boolean", required } : isLiteralUnionSchema(definedSchemas) || isEnumSchema(definedSchemas) ? { name: "enum", value: definedSchemas.map((literal) => literal.replace(/"/g, "")), required } : definedSchemas.length === 1 ? convertVueComponentMetaProp({ schema: definedSchemas[0], type: propInfo.type, required }) : (definedSchemas.length > 2 && definedSchemas.includes("true") && definedSchemas.includes("false") && (definedSchemas = definedSchemas.filter((i) => i !== "true" && i !== "false"), definedSchemas.push("boolean")), { name: "union", value: definedSchemas.map((i) => convertVueComponentMetaProp(typeof i == "object" ? { schema: i, type: i.type, required: false } : { schema: i, type: i, required: false })), required });
    }
    case "array": {
      let definedSchemas = schema.schema?.filter((item) => item !== "undefined") ?? [];
      return definedSchemas.length === 0 ? fallbackSbType : definedSchemas.length === 1 ? { name: "array", value: convertVueComponentMetaProp({ schema: definedSchemas[0], type: propInfo.type, required: false }), required } : { name: "union", value: definedSchemas.map((i) => convertVueComponentMetaProp(typeof i == "object" ? { schema: i, type: i.type, required: false } : { schema: i, type: i, required: false })), required };
    }
    case "object":
      return { name: "object", value: {}, required };
    default:
      return fallbackSbType;
  }
};
var formatDescriptionWithTags = (description, tags) => !tags?.length || !description ? description ?? "" : `${tags.map((tag) => `@${tag.name}: ${tag.text}`).join("<br>")}<br><br>${description}`;
var isLiteralUnionSchema = (schemas) => schemas.every((schema) => typeof schema == "string" && schema.startsWith('"') && schema.endsWith('"'));
var isEnumSchema = (schemas) => schemas.every((schema) => typeof schema == "string" && schema.includes("."));
var isBooleanSchema = (schemas) => schemas.length === 2 && schemas.includes("true") && schemas.includes("false");
var TRACKING_SYMBOL = Symbol("DEEP_ACCESS_SYMBOL");
var isProxy = (obj) => !!(obj && typeof obj == "object" && TRACKING_SYMBOL in obj);
var sourceDecorator = (storyFn, ctx) => {
  let story = storyFn();
  if (shouldSkipSourceCodeGeneration(ctx)) return story;
  let channel = import_preview_api.addons.getChannel();
  return watch(() => ctx.args, () => {
    let sourceCode = generateSourceCode(ctx);
    channel.emit(yn, { id: ctx.id, args: ctx.args, source: sourceCode, format: "vue" });
  }, { immediate: true, deep: true }), story;
};
var generateSourceCode = (ctx) => {
  let sourceCodeContext = { imports: {}, scriptVariables: {} }, { displayName, slotNames, eventNames } = parseDocgenInfo(ctx.component), props = generatePropsSourceCode(ctx.args, slotNames, eventNames, sourceCodeContext), slotSourceCode = generateSlotSourceCode(ctx.args, slotNames, sourceCodeContext), componentName = displayName || ctx.title.split("/").at(-1), templateCode = slotSourceCode ? `<${componentName} ${props}> ${slotSourceCode} </${componentName}>` : `<${componentName} ${props} />`, variablesCode = Object.entries(sourceCodeContext.scriptVariables).map(([name, value]) => `const ${name} = ${value};`).join(`

`), importsCode = Object.entries(sourceCodeContext.imports).map(([packageName, imports]) => `import { ${Array.from(imports.values()).sort().join(", ")} } from "${packageName}";`).join(`
`), template = `<template>
  ${templateCode}
</template>`;
  return !importsCode && !variablesCode ? template : `<script lang="ts" setup>
${importsCode ? `${importsCode}

${variablesCode}` : variablesCode}
<\/script>

${template}`;
};
var shouldSkipSourceCodeGeneration = (context) => {
  let sourceParams = context?.parameters.docs?.source;
  return sourceParams?.type === gt.DYNAMIC ? false : !context?.parameters.__isArgsStory || sourceParams?.code || sourceParams?.type === gt.CODE;
};
var parseDocgenInfo = (component) => {
  if (!component || !("__docgenInfo" in component) || !component.__docgenInfo || typeof component.__docgenInfo != "object") return { displayName: component?.__name, eventNames: [], slotNames: [] };
  let docgenInfo = component.__docgenInfo, displayName = "displayName" in docgenInfo && typeof docgenInfo.displayName == "string" ? docgenInfo.displayName : void 0, parseNames = (key) => !(key in docgenInfo) || !Array.isArray(docgenInfo[key]) ? [] : docgenInfo[key].map((i) => i && typeof i == "object" && "name" in i ? i.name : void 0).filter((i) => typeof i == "string");
  return { displayName: displayName || component.__name, slotNames: parseNames("slots").sort((a, b2) => a === "default" ? -1 : b2 === "default" ? 1 : a.localeCompare(b2)), eventNames: parseNames("events") };
};
var generatePropsSourceCode = (args, slotNames, eventNames, ctx) => {
  let properties = [];
  Object.entries(args).forEach(([propName, value]) => {
    if (!slotNames.includes(propName) && value != null) switch (isProxy(value) && (value = value.toString()), typeof value) {
      case "string":
        if (value === "") return;
        properties.push({ name: propName, value: value.includes('"') ? `'${value}'` : `"${value}"`, templateFn: (name, propValue) => `${name}=${propValue}` });
        break;
      case "number":
        properties.push({ name: propName, value: value.toString(), templateFn: (name, propValue) => `:${name}="${propValue}"` });
        break;
      case "bigint":
        properties.push({ name: propName, value: `BigInt(${value.toString()})`, templateFn: (name, propValue) => `:${name}="${propValue}"` });
        break;
      case "boolean":
        properties.push({ name: propName, value: value ? "true" : "false", templateFn: (name, propValue) => propValue === "true" ? name : `:${name}="false"` });
        break;
      case "symbol":
        properties.push({ name: propName, value: `Symbol(${value.description ? `'${value.description}'` : ""})`, templateFn: (name, propValue) => `:${name}="${propValue}"` });
        break;
      case "object": {
        properties.push({ name: propName, value: formatObject(value ?? {}), templateFn: void 0 });
        break;
      }
    }
  }), properties.sort((a, b2) => a.name.localeCompare(b2.name));
  let props = [];
  return properties.forEach((prop) => {
    let isVModel = eventNames.includes(`update:${prop.name}`);
    if (!isVModel && prop.templateFn) {
      props.push(prop.templateFn(prop.name, prop.value));
      return;
    }
    let variableName = prop.name;
    if (variableName in ctx.scriptVariables) {
      let index = 1;
      do
        variableName = `${prop.name}${index}`, index++;
      while (variableName in ctx.scriptVariables);
    }
    if (!isVModel) {
      ctx.scriptVariables[variableName] = prop.value, props.push(`:${prop.name}="${variableName}"`);
      return;
    }
    ctx.scriptVariables[variableName] = `ref(${prop.value})`, ctx.imports.vue || (ctx.imports.vue = /* @__PURE__ */ new Set()), ctx.imports.vue.add("ref"), prop.name === "modelValue" ? props.push(`v-model="${variableName}"`) : props.push(`v-model:${prop.name}="${variableName}"`);
  }), props.join(" ");
};
var generateSlotSourceCode = (args, slotNames, ctx) => {
  let slotSourceCodes = [];
  return slotNames.forEach((slotName) => {
    let arg = args[slotName];
    if (!arg) return;
    let slotContent = generateSlotChildrenSourceCode([arg], ctx);
    if (!slotContent) return;
    let slotBindings = typeof arg == "function" ? getFunctionParamNames(arg) : [];
    slotName === "default" && !slotBindings.length ? slotSourceCodes.push(slotContent) : slotSourceCodes.push(`<template ${slotBindingsToString(slotName, slotBindings)}>${slotContent}</template>`);
  }), slotSourceCodes.join(`

`);
};
var generateSlotChildrenSourceCode = (children, ctx) => {
  let slotChildrenSourceCodes = [], generateSingleChildSourceCode = (child) => {
    if (isVNode(child)) return generateVNodeSourceCode(child, ctx);
    switch (typeof child) {
      case "string":
      case "number":
      case "boolean":
        return child.toString();
      case "object":
        return child === null ? "" : Array.isArray(child) ? child.map(generateSingleChildSourceCode).filter((code) => code !== "").join(`
`) : JSON.stringify(child);
      case "function": {
        let paramNames = getFunctionParamNames(child).filter((param) => !["{", "}"].includes(param)), proxied = {};
        paramNames.forEach((param) => {
          proxied[param] = new Proxy({ [TRACKING_SYMBOL]: true }, { get: (t, key) => key === TRACKING_SYMBOL ? t[TRACKING_SYMBOL] : [Symbol.toPrimitive, Symbol.toStringTag, "toString"].includes(key) ? () => `{{ ${param} }}` : key === "v-bind" ? `${param}` : `{{ ${param}.${key.toString()} }}`, ownKeys: () => ["v-bind"], getOwnPropertyDescriptor: () => ({ configurable: true, enumerable: true, value: param, writable: true }) });
        });
        let returnValue = child(proxied);
        return generateSlotChildrenSourceCode([returnValue], ctx).replaceAll(/ (\S+)="{{ (\S+) }}"/g, ' :$1="$2"');
      }
      case "bigint":
        return `{{ BigInt(${child.toString()}) }}`;
      default:
        return "";
    }
  };
  return children.forEach((child) => {
    let sourceCode = generateSingleChildSourceCode(child);
    sourceCode !== "" && slotChildrenSourceCodes.push(sourceCode);
  }), slotChildrenSourceCodes.join(`
`);
};
var generateVNodeSourceCode = (vnode, ctx) => {
  let componentName = getVNodeName(vnode), childrenCode = "";
  typeof vnode.children == "string" ? childrenCode = vnode.children : Array.isArray(vnode.children) ? childrenCode = generateSlotChildrenSourceCode(vnode.children, ctx) : vnode.children && (childrenCode = generateSlotSourceCode(vnode.children, Object.keys(vnode.children).filter((i) => i !== "$stable"), ctx));
  let props = vnode.props ? generatePropsSourceCode(vnode.props, [], [], ctx) : "";
  return childrenCode ? `<${componentName}${props ? ` ${props}` : ""}>${childrenCode}</${componentName}>` : `<${componentName}${props ? ` ${props}` : ""} />`;
};
var getVNodeName = (vnode) => {
  if (typeof vnode.type == "string") return vnode.type;
  if (typeof vnode.type == "object") {
    if ("name" in vnode.type && vnode.type.name) return vnode.type.name;
    if ("__name" in vnode.type && vnode.type.__name) return vnode.type.__name;
  }
  return "component";
};
var getFunctionParamNames = (func) => {
  let STRIP_COMMENTS = /((\/\/.*$)|(\/\*[\s\S]*?\*\/))/gm, ARGUMENT_NAMES = /([^\s,]+)/g, fnStr = func.toString().replace(STRIP_COMMENTS, ""), result = fnStr.slice(fnStr.indexOf("(") + 1, fnStr.indexOf(")")).match(ARGUMENT_NAMES);
  return result ? result.flatMap((param) => {
    if (["{", "}"].includes(param)) return param;
    let nonMinifiedName = param.split(":")[0].trim();
    return nonMinifiedName.startsWith("{") ? ["{", nonMinifiedName.substring(1)] : param.endsWith("}") && !nonMinifiedName.endsWith("}") ? [nonMinifiedName, "}"] : nonMinifiedName;
  }) : [];
};
var slotBindingsToString = (slotName, params) => params.length ? params.length === 1 ? `#${slotName}="${params[0]}"` : `#${slotName}="{ ${params.filter((i) => !["{", "}"].includes(i)).join(", ")} }"` : `#${slotName}`;
var formatObject = (obj) => Object.values(obj).every((value) => value == null || typeof value != "object") ? JSON.stringify(obj) : JSON.stringify(obj, null, 2);
var parameters = { docs: { story: { inline: true }, extractArgTypes, extractComponentDescription: nn } };
var decorators = [sourceDecorator];
var argTypesEnhancers = [cn];
export {
  argTypesEnhancers,
  decorators,
  parameters
};
//# sourceMappingURL=@storybook_vue3_dist_entry-preview-docs__mjs.js.map
