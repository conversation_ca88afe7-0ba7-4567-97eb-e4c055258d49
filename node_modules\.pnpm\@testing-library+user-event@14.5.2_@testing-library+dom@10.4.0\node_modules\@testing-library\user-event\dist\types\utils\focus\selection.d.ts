import { ClickableInputOrButton } from '../click/isClickableInput';
import { EditableInputOrTextarea } from '../edit/isEditable';
/**
 * Determine if the element has its own selection implementation
 * and does not interact with the Document Selection API.
 */
export declare function hasOwnSelection(node: Node): node is EditableInputOrTextarea;
export declare function hasNoSelection(node: Node): node is ClickableInputOrButton;
